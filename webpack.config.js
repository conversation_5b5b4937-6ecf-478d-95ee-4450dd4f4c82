const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const fragmentConfig = require("./src/config/fragmentConfig.json");
const TerserPlugin = require('terser-webpack-plugin');
const CopyPlugin = require("copy-webpack-plugin");

module.exports = {
  entry: fragmentConfig,
  output: {
    filename: 'js/[name].js',
    path: path.resolve(__dirname, 'build'),
    library: 'HeaderLibrary',  // Expose the module globally as `HeaderLibrary`
    libraryTarget: 'umd',  // Universal Module Definition (UMD) for compatibility in different environments
  },
  mode: 'production',
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader'
        }
      },
      {
        test: /\.css$/,
        use: [MiniCssExtractPlugin.loader, 'css-loader'],
      },
      {
        test: /\.(png|jpg|svg|gif)$/,
        use: {
          loader: 'file-loader',
          options: {
            name: 'images/[name].[ext]',
          }
        }
      }
    ],
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: 'public/index.html',
      excludeChunks: ['static/js/bundle'],
    }),
    new MiniCssExtractPlugin({
      filename: 'styles/[name].css',
      chunkFilename: 'styles/[id].css',
    }),
    new CopyPlugin({
      patterns: [
        { from: "public/assets/js/jsencrypt.min.js", to: "ext_js/jsencrypt.min.js" },
        { from: "public/assets/keys/rsa_2084_public_key.pem", to: "keys/rsa_2084_public_key.pem" }
      ],
    })
  ],
  optimization: {
    minimize: true,
    minimizer: [
      new CssMinimizerPlugin(),
      new TerserPlugin()
    ]
  },
};