import React, { useState, useEffect, useCallback, useRef, startTransition, lazy } from "react";
import ReactDOM from "react-dom/client";
import { getMyChats } from "../api/chatapi";
import { v4 as uuidv4 } from 'uuid';
const ChatWindow = React.memo(lazy(() => import("../components/ChatWindow")));
function Chatbot() {
  const [isLoading, setIsLoading] = useState(false);
  const [chat, setChat] = useState([]);
  const [sessionId, setSessionId] = useState('');
  useEffect(() => {
    async function fetchData() {
      let sessionUuid = localStorage.getItem('sessionUuid');
      if (!sessionUuid) {
        sessionUuid = uuidv4();
        localStorage.setItem('sessionUuid', sessionUuid);
      }
      let fetchChats = await getMyChats(sessionUuid)
      console.log(fetchChats);
      setSessionId(sessionUuid);
      setChat(fetchChats);
      setIsLoading(true);
    }
    fetchData();
  }, []);

  return (
    <ChatWindow
      isLoading={isLoading}
      chat={chat}
      sessionId={sessionId}
    />
  );
}

export default Chatbot;

const rootDivFooter = document.createElement('div');
document.body.appendChild(rootDivFooter);

const root = ReactDOM.createRoot(rootDivFooter);
root.render(
  <div>
    <Chatbot />
  </div>
);