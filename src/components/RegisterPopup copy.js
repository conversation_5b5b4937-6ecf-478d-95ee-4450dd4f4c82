import React, { useState, useEffect } from "react";
import { Button } from "@vernost_dev/vms-react-components/dist/Button";
import { MobileInputText } from "@vernost_dev/vms-react-components/dist/MobileInputText";
import { InputText } from "@vernost_dev/vms-react-components/dist/InputText";
import { registerUser } from "../api/sso";
import styles from "../styles/register.module.css";
import getCountryData from "../api/getCountryData";
import { ToastMessage } from "@vernost_dev/vms-react-components/dist/ToastMessage";
import config from "../config/config.json";



const RegisterPopup = (props) => {
  // const [isShowLogin, setIsShowLogin] = useState(false)
  const [countryData, setcountryData] = useState([]);
  const [publicKey, setPublicKey] = useState(null);
  const [isRegisterActive, setIsRegisterActive] = useState(false);
  const [registerForm, setRegisterForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    contact_no: "",
    isd_code: "+1",
    password: "",
    confirmPassword: "",
  });
  const [registerFormErrors, setRegisterFormErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    contact_no: "",
    isd_code: "",
    password: "",
    confirmPassword: "",
  });
  const [numberLength, setNumberLength] = useState({
    minLength: 4,
    maxLength: 13,
  });
  const [salutationDropdownOpen, setsalutationDropdownOpen] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [isRegisterLoading, setIsRegisterLoading] = useState(false);
  const [showToastMessage, setShowToastMessage] = useState({
    mode: "",
    msg: "",
    isOpen: false,
  });

  const [showPassword, setShowPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleTooltip = () => {
    setShowTooltip(!showTooltip);
  };

  useEffect(() => {
    fetch(`${config.base_url_fragment}/keys/rsa_2084_public_key.pem`)
      .then((response) => response.text())
      .then((key) => setPublicKey(key))
      .catch((err) => console.error('Error loading PEM file:', err));
    getCountry()
  }, []);

  useEffect(() => {
    if (props.closePopup) {
      setRegisterForm({
        firstName: "",
        lastName: "",
        email: "",
        contact_no: "",
        isd_code: "+1",
        password: "",
        confirmPassword: "",
      });
      setIsRegisterActive(false);
      setRegisterFormErrors(
        {
          firstName: "",
          lastName: "",
          email: "",
          contact_no: "",
          isd_code: "",
          password: "",
          confirmPassword: "",
        }
      );
      props.setClosePopup(false)
    }
  }, [props.closePopup]);

  const getCountry = async () => {
    let response = await getCountryData({});
    if (response.status === true) {
      let newdata = response.data.map((ele) => ({
        code: ele.country_code,
        icon: "",
        id: ele.country_id,
        label: ele.country_isd_code,
        value: ele.country_name,
        maxLength: ele.country_contact_max_length,
        minLength: ele.country_contact_min_length,
      }));
      setcountryData(newdata);
    }
    // return response;
  };

  const myapi = async () => {
    let reqObj = {
      firstName: registerForm.firstName,
      lastName: registerForm.lastName,
      emailId: registerForm.email,
      password: registerForm.password,
      contactNo: registerForm.isd_code + registerForm.contact_no
    };

    const encryptor = new window.JSEncrypt();
    encryptor.setPublicKey(publicKey);

    const encryptedReqObj = encryptor.encrypt(JSON.stringify(reqObj));

    let apiReq = {
      "token": encryptedReqObj
    }

    let response = await registerUser(apiReq);

    if (response?.data?.status) {
      setShowToastMessage({
        mode: "success",
        msg: response.data.message,
        isOpen: true,
      });
      setRegisterForm({
        firstName: "",
        lastName: "",
        email: "",
        contact_no: "",
        isd_code: "+91",
        password: "",
        confirmPassword: "",
      })

      props.setIsResgisterPopup(false);
      props.setIsSignInPopup(true);
    } else {
      setShowToastMessage({
        mode: "error",
        msg: response.data.message,
        isOpen: true,
      });
    }

    return response;
  };

  const validateRegisterField = (name, value) => {
    setIsRegisterActive(false);
    const pwsEx = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,13}$/;
    let isValid = true;
    let errorMessage = "";

    switch (name) {
      case "email":
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!value) {
          errorMessage = props?.i18nData?.data?.PLS_ENTER_EMAIL || "Please enter email ID";
          isValid = false;
        } else if (!emailRegex.test(value)) {
          errorMessage = props?.i18nData?.data?.PLS_ENTER_VALID_EMAIL || "Please enter a valid email ID";
          isValid = false;
        }
        break;
      case "contact_no":
        if (!value) {
          errorMessage = props?.i18nData?.data?.PLS_ENTER_CONTACT_NO || "Please enter contact number ";
          isValid = false;
        } else if (
          value.length < numberLength.minLength ||
          value.length > numberLength.maxLength
        ) {
          errorMessage = props?.i18nData?.data?.PLS_ENTER_VALID_CONTACT_NO || "Please enter a valid contact number";
          isValid = false;
        }
        break;
      case "isd_code":
        if (!value) {
          errorMessage = props?.i18nData?.data?.PLS_ENTER_ISD_CODE || " Please enter ISD code ";
          isValid = false;
        }
        break;
      case 'firstName':
        if (!value) {
          errorMessage = props?.i18nData?.data?.PLS_ENTER_FIRST_NAME || 'Please enter first name ';
          isValid = false;
        }
        break;
      case 'lastName':
        if (!value) {
          errorMessage = props?.i18nData?.data?.PLS_ENTER_LAST_NAME || 'Please enter last name ';
          isValid = false;
        }
        break;
      case 'password':
        if (!value) {
          errorMessage = props?.i18nData?.data?.PLS_ENTER_PASSWORD || 'Please enter password ';
          isValid = false;
        } else if (!pwsEx.test(value)) {
          errorMessage = props?.i18nData?.data?.PLS_ENTER_VALID_PASSWORD || 'Please enter a valid password';
          isValid = false;
        }
        break;
      case 'confirmPassword':
        if (!value) {
          errorMessage = props?.i18nData?.data?.PLS_ENTER_CONFIRM_PASSWORD || 'Please enter confirm password ';
          isValid = false;
        } else if (value !== registerForm.password) {
          errorMessage = props?.i18nData?.data?.PASSWORD_MISMATCH || 'Passwords does not match';
          isValid = false;
        }
        break;
      default:
        break;
    }

    setRegisterFormErrors(prevErrors => ({
      ...prevErrors,
      [name]: errorMessage
    }));
    if (!checkIfAnyFieldEmpty()) {
      setIsRegisterActive(true);
    }
    if (!isValid) {
      setIsRegisterActive(false);
    }
    return isValid;
  };

  const handleBlur = (event) => {
    const { name, value } = event.target;
    validateRegisterField(name, value);
  };
  const getMaxLength = (countryData, value) => {
    let selectedCountry = countryData.filter((ele) => ele.label == value);

    let minLength =
      selectedCountry?.[0]?.minLength != null
        ? selectedCountry?.[0]?.minLength
        : 4;
    let maxLength =
      selectedCountry?.[0]?.maxLength != null
        ? selectedCountry?.[0]?.maxLength
        : 13;
    let length = {
      minLength,
      maxLength,
    };
    return length;
  };


  const handleRegisterFormChange = (value, name, country) => {

    if ((name === "firstName" || name === "lastName") && !/^[a-zA-Z]*$/.test(value)) {
      return; // Ignore the input if it contains non-alphabet characters
    }

    setRegisterForm((prevForm) => {
      const updatedForm = { ...prevForm };
      updatedForm[name] = value;
      return updatedForm;
    });

    if (name === "isd_code") {
      setNumberLength(getMaxLength(countryData, value));
    }

    validateRegisterField(name, value);

    setRegisterFormErrors((prevErrors) => ({
      ...prevErrors,
      [name]: "",
    }));
  };
  const handleRegisterFormChangeError = (value, name) => {
    validateRegisterField(name, value);
    // setRegisterFormErrors((prevErrors) => ({
    //   ...prevErrors,
    //   [name]: error,
    // }));
  };

  const checkIfAnyFieldEmpty = () => {
    let isAnyFieldEmpty = false;
    Object.keys(registerForm).forEach((fieldName) => {
      if (!registerForm[fieldName]) {
        isAnyFieldEmpty = true;
      }
    });
    return isAnyFieldEmpty
  }



  const handleSubmit = async (event) => {
    setIsRegisterLoading(true);
    event.preventDefault();
    let formIsValid = true;
    Object.keys(registerForm).forEach(fieldName => {
      const isValidField = validateRegisterField(fieldName, registerForm[fieldName]);
      if (!isValidField) {
        formIsValid = false;
      }
    });
    if (formIsValid) {
      await myapi();
    } else {
      console.log("Form is not valid");
    }
    setIsRegisterLoading(false);
  };
  const openSignInPopup = () => {
    props.setIsResgisterPopup(false)
    props.setIsSignInPopup(true)
  }

  const onCountryVisibilityChange = () => { };


  return (
    <div className={styles.register_form}>

      <div className={styles.form_box}>
        <form>
          <h1 className={styles.register_text}>{props?.i18nData?.data?.SIGN_UP || "Sign up"}</h1>
          <div className={styles.first_name}>
            <InputText
              onChange={(e) =>
                handleRegisterFormChange(e.target.value, e.target.name)
              }
              onBlur={(e) =>
                // handleRegisterFormChangeError(e.target.value, e.target.name)
                handleBlur(e)
              }
              label={props?.i18nData?.data?.FIRST_NAME || "First name"}
              placeholder={props?.i18nData?.data?.ENTER_FIRST_NAME || "Enter First Name"}
              messageMode="error"
              msgText={registerFormErrors.firstName}
              name="firstName"
              type="text"
              value={registerForm.firstName}
            />
          </div>
          <div className={styles.last_name}>
            <InputText
              onChange={(e) =>
                handleRegisterFormChange(e.target.value, e.target.name)
              }
              onBlur={(e) =>
                // handleRegisterFormChangeError(e.target.value, e.target.name)
                handleBlur(e)
              }
              label={props?.i18nData?.data?.LAST_NAME || "Last name"}
              placeholder={props?.i18nData?.data?.ENTER_LAST_NAME || "Enter Last Name"}
              messageMode="error"
              msgText={registerFormErrors.lastName}
              name="lastName"
              type="text"
              value={registerForm.lastName}
            />
          </div>
          <div className={styles.email_add}>
            <div className={styles.brp_contctinfo_input_allsection}>
              <InputText
                onChange={(e) =>
                  handleRegisterFormChange(e.target.value, e.target.name)
                }
                onBlur={(e) =>
                  // handleRegisterFormChangeError(e.target.value, e.target.name)
                  handleBlur(e)
                }
                label={props?.i18nData?.data?.EMAIL_ADDRESS || "Email address"}
                placeholder={props?.i18nData?.data?.EMAIL_ADDRESS || "Email address"}
                messageMode="error"
                msgText={registerFormErrors.email}
                name="email"
                type="email"
                value={registerForm.email}
              />
            </div>
          </div>
          <div className={styles.mobile_country_section}>
            <MobileInputText
              open={salutationDropdownOpen}
              maxLength={numberLength.maxLength}
              // onCountrydDropDownBlur={(e) =>
              //   handleRegisterFormChangeError(e.target.value, "isd_code")
              // }
              onMobileNoBlur={(e) => {
                handleRegisterFormChangeError(e.target.value, "contact_no");
              }}
              arrData={countryData}
              listItems={"code"}
              inputlabel={props?.i18nData?.data?.ISD_CODE || "Isd Code"}
              mobileNumber={registerForm.contact_no}
              selectedValueFilterBy="label"
              selectedValue={registerForm.isd_code}
              name="contact_no"
              placeholder={props?.i18nData?.data?.MOBILE_NUMBER || " Mobile number"}
              value={registerForm.contact_no}
              onCountryChange={(e) => handleRegisterFormChange(e, "isd_code")}
              type="text"
              onMobileNumberChange={(e) =>
                handleRegisterFormChange(e, "contact_no")
              }
              onCountryCodeSelect={(val, item) => {
                handleRegisterFormChange(item.label, "isd_code", val);
              }}
              onCountryVisibilityChange={(val) =>
                onCountryVisibilityChange(val)
              }
              msgText={registerFormErrors.contact_no}
              isdCodeErrorMsgText={registerFormErrors.isd_code}
            />
          </div>
          <div className={styles.password}>
            <InputText
              onChange={(e) =>
                handleRegisterFormChange(e.target.value, e.target.name)
              }
              onBlur={(e) =>
                // handleRegisterFormChangeError(e.target.value, e.target.name)
                handleBlur(e)
              }
              label={props?.i18nData?.data?.PASSWORD || "Password"}
              placeholder={props?.i18nData?.data?.ENTER_PASSWORD || "Enter Password"}
              messageMode="error"
              msgText={registerFormErrors.password}
              name="password"
              type={"password"}
              value={registerForm.password}
              rightView={
                <div className={styles.tooltipIcon}
                  onMouseEnter={toggleTooltip}
                  onMouseLeave={toggleTooltip}
                  onClick={toggleTooltip}
                >
                  <img src='/assets/images/info.svg' alt='' />
                  {showTooltip && (
                    <div className={styles.tooltip}>
                      <p>{props?.i18nData?.data?.PASSWORD_RULE || `Password must be between 8-13 characters in length and must comprise
                      of at least 1 lowercase alphabet (a-z), 1 uppercase alphabet (A-Z), 1 number (0-9) and 1 special character`}</p>
                    </div>
                  )}
                </div>
              }
            />

          </div>
          <div className={styles.confirm_password}>
            <InputText
              onChange={(e) =>
                handleRegisterFormChange(e.target.value, e.target.name)
              }
              onBlur={(e) =>
                // handleRegisterFormChangeError(e.target.value, e.target.name)
                handleBlur(e)
              }
              label={props?.i18nData?.data?.CONFIRM_PASSWORD || "Confirm Password"}
              placeholder={props?.i18nData?.data?.CONFIRM_PASSWORD_LABLE || "Enter confirm Password"}
              messageMode="error"
              msgText={registerFormErrors.confirmPassword}
              name="confirmPassword"
              type={showPassword ? 'text' : 'password'}
              value={registerForm.confirmPassword}
              disabled={registerForm.password === ''}
              // rightView={
              //   <div className={styles.tooltipIcon}
              //   >
              //     <img src={showPassword ? "/assets/images/eye-on.svg" : "/assets/images/eye-off.svg"} alt="eye" onClick={togglePasswordVisibility} />

              //   </div>
              // }
            />
          </div>
          <Button
            buttonType="primary"
            className={styles.register_signup}
            isLoading={isRegisterLoading}
            disabled={!isRegisterActive}
            onClick={(e) => {
              handleSubmit(e)
              e.preventDefault();
            }}
          >
            {props?.i18nData?.data?.SIGN_UP || "Sign up"}
          </Button>

          {showToastMessage.isOpen && (
            <div className={styles.toastMessage}>
              <ToastMessage
                mode={showToastMessage.mode}
                autoClose={true}
                delayTime={5000}
                // dir={"right"}
                onDissmiss={() => {
                  setShowToastMessage({ isOpen: false, message: null });
                  // setS
                }}
              >
                {showToastMessage.msg}
              </ToastMessage>
            </div>
          )}
          <p className={styles.no_account}> {props?.i18nData?.data?.ALREADY_HAVE_AN_ACCOUNT || "Already have a Curacao Travel Account?"} </p>
          <Button
            buttonType="primary"
            className={styles.register_btn}
            onClick={(e) => {

              e.preventDefault();
              openSignInPopup()
            }}
          >
            {props?.i18nData?.data?.SIGN_IN || "Sign in"}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default RegisterPopup;
