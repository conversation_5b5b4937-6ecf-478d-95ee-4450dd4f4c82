import React, { useState, useEffect, useCallback, useRef, startTransition, lazy } from "react";
import { FaMicrophone, FaPaperPlane, FaTimes, FaTrash } from 'react-icons/fa'; // Microphone icon

import css from "../styles/chatbot.module.css";
import common from "../common_js/common";
import { sendChat, clearChats, getHotelSearch, getFlightSearch } from "../api/chatapi";
const { getClassName } = common;

const ChatWindow = (props) => {
  const [message, setMessage] = useState('');
  const [openChat, setOpenChat] = useState(false);
  const [chat, setChat] = useState(props.chat);
  const [responseInProgress, setResponseInProgress] = useState(false);
  const [isListening, setIsListening] = useState(false); // Track listening state
  const recognitionRef = useRef(null); // Reference to the SpeechRecognition instance
  const chatEndRef = useRef(null);

  const scrollToBottom = () => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    recognition.lang = 'en-US'; // Set language
    recognition.interimResults = true; // Only final results
    recognition.maxAlternatives = 1;
    recognition.onend = function () {
      console.log('Speech recognition has ended.');
      setTimeout(() => { handleSendMessage(); }, 1000);
      setIsListening(false);
      // recognition.start();
    };
    recognition.onresult = (event) => {
      const transcript = event.results[0][0].transcript;
      console.log('Speech recognition result:', transcript)
      setMessage(transcript); // Update input with spoken text
    };

    recognition.onerror = (error) => console.error('Speech recognition error:', error);

    recognitionRef.current = recognition; // Store instance

    return () => recognition.stop(); // Cleanup on unmount
  }, []);

  const toggleListening = () => {
    if (isListening) {
      recognitionRef.current.stop();
    } else {
      recognitionRef.current.start();
    }
    setIsListening(!isListening);
  };

  const toggleChat = useCallback(() => {
    setOpenChat(!openChat);
    scrollToBottom();
  }, [openChat]);

  useEffect(() => {
    scrollToBottom();
  }, [chat, responseInProgress]);

  const handleSendMessage = async () => {
    let testMsg = document.getElementById("messageText").value;
    if (testMsg.trim()) {
      setChat((prevChat) => [...prevChat, { sender: 'User', message: testMsg.trim(), time: new Date().toLocaleTimeString() }]);
      //socket.emit('userMessage', message);
      setMessage('');
      setResponseInProgress(true)
      let chatResponse = await sendChat(props.sessionId, testMsg);
      console.log(chatResponse);
      if (chatResponse.status) {
        setChat((prevChat) => [...prevChat, { sender: 'Vernost', message: chatResponse.data.message, time: new Date().toLocaleTimeString() }]);
      }
      if (chatResponse.data.parsed) {
        if (chatResponse.data.data.hotels) {
          let hotelResult = await getHotelSearch(chatResponse.data.data);
          if (!hotelResult.status) {
            setChat((prevChat) => [...prevChat, { sender: 'Vernost', message: "Something went wrong, please try again!", time: new Date().toLocaleTimeString() }]);
          } else {
            hotelResults(hotelResult.data)
          }
        }
        if (chatResponse.data.data.flights) {
          let flightResult = await getFlightSearch(chatResponse.data.data);
          if (!flightResult.status || !flightResult.data.length) {
            setChat((prevChat) => [...prevChat, { sender: 'Vernost', message: "Something went wrong, please try again!", time: new Date().toLocaleTimeString() }]);
          } else {
            flightResults(flightResult.data[0].onward_resp)
          }
        }
      }
      setResponseInProgress(false)
    }
  };

  const clearChat = async () => {
    let chatResponse = await clearChats(props.sessionId);
    setChat([]);
  };

  const hotelResults = (hotels) => {
    hotels = hotels.slice(0, 5);
    let result = "";
    hotels.forEach(hotel => {
      result += `<div class="${getClassName(css["hotel-card"])}">
          <div class="${getClassName(css["hotel-image"])}">
            <img src="${hotel.image}" alt="${hotel.hotel_name}" />
          </div>
          <div class="${getClassName(css["hotel-details"])}">
            <h4>${hotel.hotel_name}</h4>
            <div class="${getClassName(css["hotel-rating"])}">
              <span>${hotel.rating} <i class="fa fa-star"></i></span>
              <span>${hotel.location.label}</span>
            </div>
            <div class="${getClassName(css["hotel-price"])}">
              <span>${hotel.price} ${hotel.currency_code}</span>
              <button onClick="window.location.href='${hotel.hdp_redirect_url}'">Book Now</button>
            </div>
          </div>
        </div>`
    });
    result += `<div class="${getClassName(css["view-more"])}"><button onClick="window.location.href='${hotels[0].srp_redirect_url}'">View More</button></div>`;

    setChat((prevChat) => [...prevChat, { sender: 'Vernost', message: "Here are some hotels for you", time: new Date().toLocaleTimeString() }]);
    setChat((prevChat) => [...prevChat, { sender: '', message: result, time: new Date().toLocaleTimeString() }]);
  }

  const flightResults = (flights) => {
    console.log("Flights", flights);
    flights = flights.slice(0, 5);
    let result = "";
    flights.forEach(flight => {
      result += `<div class="${getClassName(css["flight-card"])}">
        <div class="${getClassName(css["flight-details"])}">
          <h4>${flight.flight_details.airline_name}</h4>
          <div class="${getClassName(css["flight-time"])}">
            <span>Dep: ${flight.flight_details.dept_time}</span>
            <span>Arr: ${flight.flight_details.arr_time}</span>
          </div>
          <div class="${getClassName(css["flight-price"])}">
            <span>${flight.currencyCode} ${flight.total_price}</span>
            <button>Book Now</button>
          </div>
        </div>
      </div>`
    });
    setChat((prevChat) => [...prevChat, { sender: 'Vernost', message: "Here are some flights for you", time: new Date().toLocaleTimeString() }]);
    setChat((prevChat) => [...prevChat, { sender: '', message: result, time: new Date().toLocaleTimeString() }]);
  }

  return (
    <>
      {openChat ? (
        <>
          <div className={getClassName(css["chat-box"])}>
            <div className={getClassName(css["chat-box-header"])}>
              <h3>Chat with us</h3>
              <p>
                <FaTimes onClick={toggleChat} />
              </p>
            </div>
            <div className={getClassName(css["chat-box-body"])}>
              <div id="chatHistory">
                {chat.map((msg, index) => (
                  <div key={index} className={getClassName(css["chat-box-body-" + (msg.sender == "User" ? "send" : "receive")])}>
                    {msg.sender == "Vernostt" && <b>{msg.sender}</b>}
                    <p dangerouslySetInnerHTML={{ __html: msg.message }}></p>
                    <span>{msg.time}</span>
                  </div>
                ))}
              </div>
              {responseInProgress && (
                <div className={getClassName(css["chat-loading-dots"])}>
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              )}
              <div className={getClassName(css["chat-loading-reference"])} ref={chatEndRef} />
            </div>
            <div className={getClassName(css["chat-box-footer"])}>
              <div className={getClassName(css["input-wrapper"])}>
                <input
                  placeholder="Enter Your Message"
                  id="messageText"
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                />
                <button onClick={toggleListening}>
                  <FaMicrophone
                    className={isListening ? getClassName(css["pulse"]) : ""} />
                </button>
              </div>
              <div className={getClassName(css["chat-attach"])}>
              <FaPaperPlane className={getClassName(css["send"])} onClick={handleSendMessage} />
              <FaTrash className={getClassName(css["clear"])} onClick={clearChat} />
              </div>
            </div>
          </div>
        </>
      ) :
        <>
          <div className={getClassName(css["chat-button"])} onClick={toggleChat}>
            <span><svg id="ic_bubble" fill="#FFFFFF" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true"><path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"></path><path d="M0 0h24v24H0z" fill="none"></path></svg></span>

            <div className={getClassName(css["chat-button-logo"])}>
             
            </div>
          </div>
        </>
      }
    </>
  );
}

export default ChatWindow;