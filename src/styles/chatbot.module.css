@import url("https://fonts.googleapis.com/css?family=Raleway|Ubuntu&display=swap");
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.min.css');

body {
    font-family: Raleway;
}

.chat-box {
    height: calc(100% - 70px);
    width: 360px;
    position: fixed;
    margin: 0 auto;
    overflow: auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    z-index: 1000;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.005);
    right: 0;
    bottom: 0;
    margin: 15px;
    background: #fff;
    border-radius: 15px;
    visibility: visible;
    justify-content: flex-end;
}

.chat-box-header {
    background: #0660fb;
    color: #fff;
    height: 50px;
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    justify-content: space-between;
    box-shadow: 0 1px 10px rgba(0, 0, 0, 0.025);
}

.chat-box-header h3 {
    font-family: ubuntu;
    font-weight: 400;
}

.chat-box-header p {
    cursor: pointer;
    margin: 0;
}

.chat-box-body {
    height: 100%;
    background: #f8f8f8;
    overflow-y: scroll;
    padding: 12px;
}
.chat-attach {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;

}
.chat-box-body-send {
    width: 75%;
    float: right;
    background: #c8ffe4;
    padding: 10px 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.015);
    margin-bottom: 14px;
}

.chat-box-body-send p {
    margin: 0;
    color: #444;
    font-size: 12px;
    margin-bottom: 0.25rem;
}

.chat-box-body-send b {
    color: #444;
    font-size: 14px;
    font-weight: bolder;
}

.chat-box-body-send span {
    float: right;
    color: #777;
    font-size: 10px;
}

.chat-box-body-receive {
    width: 75%;
    float: left;
    background: white;
    padding: 10px 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.015);
    margin-bottom: 14px;
}

.chat-box-body-receive p {
    margin: 0;
    color: #444;
    font-size: 12px;
    margin-bottom: 0.25rem;
}

.chat-box-body-receive b {
    margin: 0;
    color: #444;
    font-size: 14px;
    font-weight: bolder;
}

.chat-box-body-receive span {
    float: right;
    color: #777;
    font-size: 10px;
}

.chat-box-body::-webkit-scrollbar {
    width: 5px;
    opacity: 0;
}

.chat-box-footer {
    background: #0000001f;
    position: relative;
    display: flex;
    padding: 10px 15px;
    align-items: center;
    justify-content: space-between;
}

.chat-box-footer button {
    background: #00000000;
    border: none;
    padding: 16px;
    font-size: 14px;
    cursor: pointer;
}

.chat-box-footer button:focus {
    outline: none;
}

.chat-box-footer .input-wrapper {
    position: relative;
    display: flex;
    width: 83%;
}

.chat-box-footer .input-wrapper input {
    padding: 10px;
    border: none;
    -webkit-appearance: none;
    border-radius: 50px;
    background: whitesmoke;
    margin: 0;
    font-family: ubuntu;
    font-weight: 600;
    color: #444;
    width: 100%;
}

.chat-box-footer .input-wrapper button {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.chat-box-footer input:focus {
    outline: none;
}

.chat-box-footer .send {
    /* vertical-align: middle;
    align-items: center;
    justify-content: center;
    transform: translate(0px, 20px); */
    cursor: pointer;
}

.chat-box-footer .clear {
    cursor: pointer;
}

.chat-button {
    /* padding: 25px 16px;
    background: #2C50EF;
    width: 150px;
    position: fixed;
    bottom: 2px;
    right: 10px;
    margin: 15px;
    border-top-left-radius: 25px;
    border-top-right-radius: 25px;
    border-bottom-left-radius: 25px;
    box-shadow: 0 2px 15px rgba(44, 80, 239, 0.21);
    cursor: pointer; */

    padding: 0;
    background: #2C50EF;
    width: 60px;
    height: 60px;
    position: fixed;
    bottom: 18px;
    right: 18px;
    margin: 0;
    border-radius: 50%;
    box-shadow: 0 2px 15px rgba(44, 80, 239, 0.21);
    cursor: pointer;
}
.chat-button span{
    position: absolute;
    top: 30%;
    left: 30%;
}
.chat-button-logo{
    position: absolute;
    top: 30%;
    left: 30%;
}
/* .chat-button span::before {
    content: "";
    height: 15px;
    width: 15px;
    background: #47cf73;
    position: absolute;
    transform: translate(0, -7px);
    border-radius: 15px;
} */

/* .chat-button span::after {
    content: "Chat with us";
    font-size: 14px;
    color: white;
    position: absolute;
    left: 50px;
    top: 18px;
} */

.modal {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transform: scale(1.1);
    transition: visibility 0s linear 0.25s, opacity 0.25s 0s, transform 0.25s;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 1rem 1.5rem;
    width: 24rem;
    border-radius: 0.5rem;
}

.modal-close-button {
    float: right;
    width: 1.5rem;
    line-height: 1.5rem;
    text-align: center;
    cursor: pointer;
    border-radius: 0.25rem;
    background-color: lightgray;
}

.close-button:hover {
    background-color: darkgray;
}

.show-modal {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
    transition: visibility 0s linear 0s, opacity 0.25s 0s, transform 0.25s;
    z-index: 30;
}

@media only screen and (max-width: 500px) {
    .chat-box {
        width: 100% !important;
        margin:0;
        height:100%;
    }
    .chat-attach {
        height: 100%;
    }
   
}
@media (min-width: 400px) and (max-width: 500px) {
    .chat-box-footer .input-wrapper {
        width: 85%;
    }

}

.chat-loading-dots {
    width: 50px;
    float: left;
    padding: 10px 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.015);
    margin-bottom: 14px;
}

.chat-loading-dots {
    display: flex;
    gap: 5px;
    justify-content: left;
    align-items: left;
}

.chat-loading-reference {
    width: 50px;
    float: left;
    padding: 10px 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.015);
    margin-bottom: 14px;
    display: flex;
    gap: 5px;
    justify-content: left;
    align-items: left;
}

.chat-loading-dots span {
    width: 8px;
    height: 8px;
    background-color: #007bff;
    border-radius: 50%;
    animation: bounce 1.5s infinite;
}

.chat-loading-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.chat-loading-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {

    0%,
    80%,
    100% {
        transform: scale(0);
    }

    40% {
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 10px #684dff, 0 0 20px #684dff;
    }

    50% {
        box-shadow: 0 0 20px #8093ff, 0 0 40px #8093ff;
    }

    100% {
        box-shadow: 0 0 10px #684dff, 0 0 20px #684dff;
    }
}

/* Add the blinking class */
.pulse {
    animation: pulse 1s infinite;
}



.hotel-card {
    display: flex;
    flex-direction: column;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 16px;
    background-color: #fff;
}

.hotel-image img {
    width: 100%;
    height: auto;
    display: block;
}

.hotel-details {
    padding: 16px;
}

.hotel-details h4 {
    margin: 0 0 8px;
    font-size: 1.25em;
    color: #333;
}

.hotel-rating {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.hotel-rating span {
    font-size: 0.875em;
    color: #777;
}

.hotel-rating .fa-star {
    color: #f5c518;
}

.hotel-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hotel-price span {
    font-size: 1em;
    font-weight: bold;
    color: #333;
}

.hotel-price button {
    padding: 8px 16px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.hotel-price button:hover {
    background-color: #0056b3;
}

.view-more {
    width: 100%;
}

.view-more button {
    right: 0px;
    padding: 8px 16px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.flight-card {
    display: flex;
    flex-direction: column;
    border: 1px solid #ddd;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 16px;
    background-color: #fff;
}

.flight-details {
    padding: 16px;
}

.flight-details h4 {
    margin: 0 0 8px;
    font-size: 1.25em;
    color: #333;
}

.flight-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.flight-time span {
    font-size: 0.875em;
    color: #777;
}

.flight-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flight-price span {
    font-size: 1em;
    font-weight: bold;
    color: #333;
}

.flight-price button {
    padding: 8px 16px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.flight-price button:hover {
    background-color: #0056b3;
}