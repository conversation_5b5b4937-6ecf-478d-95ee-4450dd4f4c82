import request from "../common_js/request";
import config from "../config/config.json";

export async function getMyChats(session) {
    try {
        let chats = await request.post(config.base_url_api + "/getChats", { session }, {});
        return chats.data?.data || false;
    } catch (err) {
        return false;
    }
}

export async function sendChat(session, message) {
    try {
        let headers = {};
        let chats = await request.post(config.base_url_api + "/chat", { session, message }, headers);
        return chats.data || false;
    } catch (err) {
        return {
            status: false, statuscode: 301,
            message: "something wrong! Please try again",
            data: {}
        }
    }
}

export async function clearChats(session, message) {
    try {
        let headers = {};
        let chats = await request.post(config.base_url_api + "/clearChats", { session, message }, headers);
        return chats.data || false;
    } catch (err) {
        return {
            status: false, statuscode: 301,
            message: "something wrong! Please try again",
            data: {}
        }
    }
}

export async function getHotelSearch(data) {
    try {
        let headers = {};
        let chats = await request.post(config.base_url_api + "/hotelSearch", data, headers);
        return chats.data || false;
    } catch (err) {
        return {
            status: false, statuscode: 301,
            message: "something wrong! Please try again",
            data: {}
        }
    }
}

export async function getFlightSearch(data) {
    try {
        let headers = {};
        let chats = await request.post(config.base_url_api + "/flightSearch", data, headers);
        return chats.data || false;
    } catch (err) {
        return {
            status: false, statuscode: 301,
            message: "something wrong! Please try again",
            data: {}
        }
    }
}