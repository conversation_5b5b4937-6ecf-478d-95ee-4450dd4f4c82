import axios from "axios";
const config = require("../config/config.json");

const data = {
  commonHeaders: {
    'Content-Type': 'application/json'
  },
  timeout: 10000,
  getHeaders: function (headers) {
    return { ...headers, ...JSON.parse(JSON.stringify(this.commonHeaders)) };
  },

  post: async function (url, body, header) {
    let headers = this.getHeaders(header);
    try {
      let requestOptions = {
        method: 'POST',
        headers: headers,
        url: url,
        data: body
      }
      let response = await axios(requestOptions);
      return response;
    } catch (error) {
      console.error("Error during POST request:", error);
      throw error; // Re-throw the error for the caller to handle
    }
  },
  get: async function (url, body, header) {
    let headers = this.getHeaders(header);
    try {
      let response = await axios.get(url, body, {
        headers: headers,
        timeout: this.timeout,
        withCredentials: false
      });
      return response;
    } catch (error) {
      console.error("Error during GET request:", error);
      throw error; // Re-throw the error for the caller to handle
    }
  }

};

export default data;
