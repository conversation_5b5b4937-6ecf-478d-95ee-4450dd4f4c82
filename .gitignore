# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Node artifact files
node_modules/
chatHistory/
chatbot_build/

web_server/node_modules/
web_server/dist/
web_server/common/
web_server/common_next/
web_server/common/
fragment.zip
chatbot_build.zip

# next.js
/.next/
web_server/.next/
web_server/out/
/out/

# production
/build
web_server/build

package-lock.json
web_server/package-lock.json
# misc
.DS_Store


# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log

# Package files
*.jar

# Maven
target/

# JetBrains IDE
.idea/

# Unit test reports
TEST*.xml

# Generated by MacOS
.DS_Store

# Generated by Windows
Thumbs.db

# Applications
*.app
*.exe
*.war

# Large media files

*.tiff
*.avi
*.flv
*.mov
*.wmv

