const { BedrockRuntimeClient, ConverseCommand, InvokeModelCommand } = require('@aws-sdk/client-bedrock-runtime');
const { SystemMessage, HumanMessage, AIMessage } = require("langchain/schema");
const { LLMChain } = require("langchain/chains");
const { PromptTemplate } = require("langchain/prompts");
const fs = require("fs");

let searchPrompt = fs.readFileSync("../prompts/searchPrompt.txt", 'utf-8');

const bedrock = new BedrockRuntimeClient({
    region: 'us-east-1'
});

const config = require('../config.json');

module.exports = class RAGChatbot {
    constructor() {
        console.log('Initializing RAGChatbot...');
        const today = new Date();
        const todaysDate = today.toISOString().split('T')[0];

        this.systemPrompt = [{
            "text": searchPrompt.replace('{todaysDate}', todaysDate),
            "role": "system"
        }];
    }

    async invokeBedrock(messages) {
        try {
            const conversation = [];
            // Add conversation history
            for (let i = 0; i < messages.length; i++) {
                const msg = messages[i];
                conversation.push(
                    {
                        role: msg.sender,
                        content: [{ text: msg.message }],
                    },
                )

            }

            const params = {
                modelId: 'meta.llama3-70b-instruct-v1:0',
                messages: conversation,
                system: this.systemPrompt
            };

            const command = new ConverseCommand(params);
            const response = await bedrock.send(command);
            const result = response?.output?.message?.content.length ? response.output.message.content[0].text : "";
            return result;
        } catch (error) {
            console.error("Error invoking Bedrock:", error);
            throw error;
        }
    }

    async getResponse(query, session) {
        try {
            let conversationLog = [];
            const conversationFile = "./chatHistory/" + session + ".json";

            // Ensure chatHistory directory exists
            const dir = "./chatHistory";
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            if (fs.existsSync(conversationFile)) {
                conversationLog = JSON.parse(fs.readFileSync(conversationFile, "utf-8"));
            }

            // Convert conversation log to LangChain message format
            conversationLog.push({
                sender: 'user',
                message: query,
                time: new Date().toLocaleTimeString()
            });

            // Get response from the model
            const response = await this.invokeBedrock(conversationLog);

            let formattedResponse = this.formatResponse(response);

            // Update conversation log


            conversationLog.push({
                sender: 'assistant',
                message: formattedResponse.message,
                time: new Date().toLocaleTimeString()
            });

            // Save updated conversation
            fs.writeFileSync(conversationFile, JSON.stringify(conversationLog, null, 2));

            return formattedResponse;
        } catch (error) {
            console.error('Response generation error:', error);
            console.error('Error stack:', error.stack);
            throw error;
        }
    }

    async getChats(session) {
        try {
            const conversationFile = "./chatHistory/" + session + ".json";
            if (fs.existsSync(conversationFile)) {
                return JSON.parse(fs.readFileSync(conversationFile, "utf-8"));
            } else {
                return [{ sender: 'Vernost', message: 'Hello! How can I help you?', time: new Date().toLocaleTimeString() }];
            }
        } catch (error) {
            console.error('Chat history retrieval error:', error);
            return `Error: ${error.message}`;
        }
    }

    async clearChats(session) {
        try {
            const conversationFile = `./chatHistory/${session}/search.json`;
            if (fs.existsSync(conversationFile)) {
                fs.unlinkSync(conversationFile);
            }
            return 'Chat history cleared';
        } catch (error) {
            console.error('Chat history clear error:', error);
            return `Error: ${error.message}`;
        }
    }

    formatResponse(response) {
        let resp = response.replace(/Vernost:|Assistant:|System:|/g, '').trim();

        const firstBrace = resp.indexOf('{');
        const lastBrace = resp.lastIndexOf('}');

        // If braces are found, extract the substring
        if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
            const jsonString = resp.substring(firstBrace, lastBrace + 1);
            try {
                let parseJson = JSON.parse(jsonString);
                return { parsed: true, data: parseJson, message: this.formatMessage(parseJson) };
            } catch (error) {
                return { parsed: false, data: [], message: resp };
            }
        } else {
            return { parsed: false, data: [], message: resp };
        }
    }

    formatMessage(parseJson) {
        try {
            let message = 'Sorry I could not process the request. Please try again';
            if (parseJson.hotels) {
                message = `Thank you for providing the information. Hotel search initiated for ${parseJson.city} from ${parseJson.checkin} to ${parseJson.checkout} for ${parseJson.guests.adults} adults and ${parseJson.guests.childs} children. I will now search for the best hotel options. Please hold on for a moment while I gather the details`;
            } else if (parseJson.flights) {
                message = `Thank you for providing the information. Flight search initiated from ${parseJson.from_airportcode} to ${parseJson.to_airportcode} on ${parseJson.travelDate} for ${parseJson.guests.adults} adults, ${parseJson.guests.childs} children, and ${parseJson.guests.infants} infants. I will now search for the best flight options. Please hold on for a moment while I gather the details`;
            }
            return message;
        } catch (error) {
            return `Error: ${error.message}`;
        }
    }
};