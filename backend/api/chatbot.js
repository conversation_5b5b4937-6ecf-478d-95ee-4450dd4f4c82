const { ChatOpenAI } = require('@langchain/openai');
const { ConversationChain } = require("langchain/chains");
const { PromptTemplate } = require("langchain/prompts");
const fs = require("fs");

const config = require('../config.json');

module.exports = class RAGChatbot {
    constructor() {
        this.llm = new ChatOpenAI({
            openAIApiKey: config.OPENAI_API_KEY,
            modelName: 'gpt-3.5-turbo',
            temperature: 0.7
        });

        const promptTemplate = `
        You will play the role of <PERSON><PERSON><PERSON>, a highly knowledgeable AI. Engage in conversation with the user, providing informative and helpful responses to their queries regarding travel platforms
        Todays date is 2024-11-28.
You need to collect the following information if user is searching for an hotel:
1. City/region/airportcode.
2. Check-in and check-out dates.
3. Number of guests (pax) Adults / Childs.

You need to collect the following information if user is searching for an flight:
1. From City/Country/AirportCode.
2. To City/Country/AirportCode.
3. Date of Travel.
4. Number of guests (pax) Adults/Childrens/Infants.

Guidelines for Hotels:
** General Rule**: 
1. For general queries, respond with a **single string** (no list formatting).
2. Do not include any metadata, tags, or unnecessary formatting in the output.
3. Date Format has to be in YYYY-MM-DD format.
4. When sending JSON Response don't add any other text in the response just a plain Json structure.
5. If Check-in Date of hotels is provided the same can be used for Flights Date of Travel. Similarly, if Flights Date of Travel is provided the same can be used for Hotels Check-in Date.
6. Number of Guests can be Common for both Hotels and Flights.
7. Send same response as last response if the user has provided all required input and there is no new query with question.

1. **First, check if the query is general** (not related to flight / hotel search or booking):
   1.1 If the query is general (e.g., about Location, Nearby Attractions, or Things to do in location, where to stay), retrieve the most relevant answer.
       - Respond with a **single string** (no list formatting, no square brackets), based on the retrieved context, even if the context is not fully sufficient.

2. **If the query is about Hotel Search or Booking**:
    2.1 **Check if city/region/airportcode provided in conversation **:
        - check If Country is provided and city/region/airportcode is not provided then ask for city/region/airportcode.
        - check If city/region/airportcode is provided, check if check-in date is provided. If not, ask for check-in date.
    2.2 **check If check-in date is provided in conversation **: 
        - check if check-out date is provided. default take +2 days as check-out date from check-in. 
        - check If pax count is provided, if not take default pax to 1 adult. 
        - Childrens are not compulsory if user has not provided the input then take it as 0.
    2.3 **check If user has provided all the required information for hotel search or booking**:
        - respond with only json format with following fields in object city, checkin, checkout, guests(adults, childs), hotels: true**:
    2.4 **If any of the required input is missing, ask for the missing input**.

3. **If the query is about Flights Search or Booking**:
    3.1 **Check if To city/Country/airportcode provided in conversation **:
        - check If To Country/City is provided then get the nearest Airport Code to that Country/City.
        - check If From city/Country/airportcode is provided, if not ask for From city/Country/airportcode.
    3.2 **check If From city/Country/airportcode is provided in conversation **:
        - check If To Country/City is provided then get the nearest Airport Code to that Country/City.
        - check If Travel date is provided, if not ask for Travel date.
    3.3 **check If Travel date is provided in conversation **: 
        - check If pax count is provided, if not take default pax to 1 adult. 
        - Childrens and Infants are not compulsory if user has not provided the input then take it as 0.
    3.4 **check If user has provided all the required information for Flights search or booking**:
        - respond with only json format with following fields in object from_airportcode, to_airportcode, travelDate, guests(adults, child, infants), flights: true**:
    3.5 **If any of the required input is missing, ask for the missing input**.

Conversation:
{conversation}
`;
        this.conversationChain = new ConversationChain({
            llm: this.llm,
            prompt: new PromptTemplate({
                template: promptTemplate,
                inputVariables: ["conversation"],
            }),
        });
    }

    async initializeRAG() {
        try {

        } catch (error) {
            console.error('RAG initialization error:', error);
        }
    }

    async getResponse(query, session) {
        try {
            let conversationLog = [];
            const conversationFile = "./chatHistory/" + session + ".json";
            if (fs.existsSync(conversationFile)) {
                conversationLog = JSON.parse(fs.readFileSync(conversationFile, "utf-8"));
            } else {
                conversationLog.push({ sender: 'Vernost', message: 'Hello! How can I help you?', time: new Date().toLocaleTimeString() });
            }
            conversationLog.push({ sender: 'User', message: query, time: new Date().toLocaleTimeString() });
            const response = await this.conversationChain.call({ conversation: this.formatQuery(conversationLog) });
            let formattedResponse = this.formatResponse(response.response);
            conversationLog.push({ sender: 'Vernost', message: formattedResponse.message, time: new Date().toLocaleTimeString() });
            fs.writeFileSync(conversationFile, JSON.stringify(conversationLog, null, 2));
            return formattedResponse;
        } catch (error) {
            console.error('Response generation error:', error);
            return `Error: ${error.message}`;
        }
    }

    async getChats(session) {
        try {
            const conversationFile = "./chatHistory/" + session + ".json";
            if (fs.existsSync(conversationFile)) {
                return JSON.parse(fs.readFileSync(conversationFile, "utf-8"));
            } else {
                return [{ sender: 'Vernost', message: 'Hello! How can I help you?', time: new Date().toLocaleTimeString() }];
            }
        } catch (error) {
            console.error('Chat history retrieval error:', error);
            return `Error: ${error.message}`;
        }
    }

    formatQuery(conversationLog) {
        try {
            let query = '';
            for (let i = 0; i < conversationLog.length; i++) {
                query += `${conversationLog[i].sender}: ${conversationLog[i].message}\n`;
            }
            console.log('Formatted query:', query);
            return query;
        } catch (error) {
            console.error('Query formatting error:', error);
            return `Error: ${error.message}`;
        }
    }

    formatResponse(response) {
        let resp = response.replace(/Vernost:/g, '').replace(/User:/g, '').trim();
        try {
            let parseJson = JSON.parse(resp);
            return { parsed: true, data: parseJson, message: this.formatMessage(parseJson) };
        } catch (error) {
            return { parsed: false, data: [], message: resp };
        }
    }

    formatMessage(parseJson) {
        try {
            let message = 'Sorry I could not process the request. Please try again';
            if (parseJson.hotels) {
                message = `Thank you for providing the information. Hotel search initiated for ${parseJson.city} from ${parseJson.checkin} to ${parseJson.checkout} for ${parseJson.guests.adults} adults and ${parseJson.guests.childs} children. I will now search for the best hotel options. Please hold on for a moment while I gather the details`;
            } else if (parseJson.flights) {
                message = `Thank you for providing the information. Flight search initiated from ${parseJson.from_airportcode} to ${parseJson.to_airportcode} on ${parseJson.travelDate} for ${parseJson.guests.adults} adults, ${parseJson.guests.childs} children, and ${parseJson.guests.infants} infants.  I will now search for the best flight options. Please hold on for a moment while I gather the details`;
            }
            return message;
        } catch (error) {
            return `Error: ${error.message}`;
        }
    }

};