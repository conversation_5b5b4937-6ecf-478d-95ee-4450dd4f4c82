const BedrockLlamaClient = require('../helpers/bedrockClient');
const Hotels = require('./hotels');
const Flights = require('./flights');
const fs = require('fs');
const bedrockClient = new BedrockLlamaClient();

class StreamChatbot {
    constructor(session, message) {
        this.session = session;
        this.message = message;
        this.hotelsAPI = new Hotels();
        this.flightsAPI = new Flights();
        this.identifyFlowPrompt = fs.readFileSync('../prompts/initiate.txt', 'utf-8');
        this.searchPrompt = fs.readFileSync('../prompts/searchPrompt.txt', 'utf-8');
        this.hotelsFilterPrompt = fs.readFileSync('../prompts/hotelsFilter.txt', 'utf-8');
        this.conversationLog = this.getConversationHistory(session);
    }

    async* getResponse() {
        try {
            let conversationFor = "search";
            //check if conversationLog has type == filter
            if (this.conversationLog.some(msg => msg.type === 'filter')) {
                conversationFor = "filter";
                let messages = this.conversationLog?.map(msg => ({
                    role: msg.sender.toLowerCase(),
                    content: msg.message
                })) || [];

                messages.push({
                    role: 'user',
                    content: this.message
                });

                const conversation = await bedrockClient.converse(messages, this.identifyFlowPrompt);
                console.log("conversation", this.message, conversation);
                if (conversation.trim().includes('search')) {
                    conversationFor = "search";
                }
            }
            this.conversationLog.push({ sender: 'User', type: conversationFor, message: this.message, time: new Date().toLocaleTimeString() });

            if (conversationFor == "search") {
                yield* this.handleSearchConversation();
            } else {
                let request_id = this.conversationLog[this.conversationLog.length - 1].request_id;
                if (!request_id) {
                    for (let conv of this.conversationLog) {
                        if (conv.request_id) {
                            request_id = conv.request_id;
                        }
                    }
                }
                yield* this.handleFilterConversationHotels(request_id);
            }

        } catch (error) {
            console.error('Streaming error:', error);
            yield {
                type: 'error',
                content: error.message
            };
        }
    }

    async* handleSearchConversation() {
        try {
            let onlySeaarchConversations = this.conversationLog.filter(msg => msg.type === 'search');
            let messages = onlySeaarchConversations?.map(msg => ({
                role: msg.sender.toLowerCase(),
                content: msg.message
            })) || [];
            const streamGenerator = bedrockClient.converseStream(messages, this.searchPrompt);

            let accumulatedResponse = '';
            let jsonBuffer = '';
            let isCollectingJson = false;
            let openBraces = 0;

            for await (const chunk of streamGenerator) {
                // Add chunk to accumulated response
                accumulatedResponse += chunk;

                // Count opening and closing braces
                for (let char of chunk) {
                    if (char === '{') openBraces++;
                    if (char === '}') openBraces--;
                }

                // Start collecting JSON when first opening brace is found
                if (chunk.includes('{') && !isCollectingJson) {
                    isCollectingJson = true;
                    jsonBuffer = chunk.slice(chunk.indexOf('{'));
                    // Only yield text before the JSON
                    const textBeforeJson = chunk.slice(0, chunk.indexOf('{'));
                    if (textBeforeJson.trim()) {
                        yield {
                            type: 'thinking',
                            content: textBeforeJson
                        };
                    }
                    continue;
                }

                // Continue collecting JSON
                if (isCollectingJson) {
                    if (!chunk.includes('{')) {
                        jsonBuffer += chunk;
                    }

                    // Check if we have complete JSON (balanced braces)
                    if (openBraces === 0) {
                        isCollectingJson = false;
                        // Don't yield JSON content
                        jsonBuffer = '';
                        continue;
                    }
                    continue;
                }

                // Only yield non-JSON content when we're sure it's not part of JSON
                if (!isCollectingJson && chunk.trim() && !chunk.includes('{') && !chunk.includes('}')) {
                    yield {
                        type: 'thinking',
                        content: chunk
                    };
                }
            }

            // Process the complete response
            const parsedResponse = this.formatResponse(accumulatedResponse);

            this.conversationLog.push({
                sender: 'assistant',
                type: 'search',
                message: parsedResponse.message,
                time: new Date().toLocaleTimeString()
            });

            await this.saveConversation(this.session, this.conversationLog);
            if (parsedResponse.parsed && parsedResponse.data) {

                if (parsedResponse.data.hotels) {
                    yield {
                        type: 'hotels_loading',
                        content: parsedResponse.message
                    };

                    const hotelResults = await this.hotelsAPI.search(parsedResponse.data);
                    if (hotelResults) {
                        yield {
                            type: 'message',
                            content: 'Here are the top 5 hotel options for your search.'
                        };

                        yield {
                            type: 'hotels',
                            content: hotelResults.hotels
                        };

                        this.conversationLog.push({
                            sender: 'assistant',
                            type: 'searchReqBody',
                            message: hotelResults.searchReqBody,
                            time: new Date().toLocaleTimeString()
                        });

                        this.conversationLog.push({
                            sender: 'user',
                            type: 'filter',
                            message: 'Show me some best filters',
                            time: new Date().toLocaleTimeString(),
                            request_id: hotelResults.request_id
                        });
                        await this.saveConversation(this.session, this.conversationLog);

                        yield* this.handleFilterConversationHotels(hotelResults.request_id);

                    } else {
                        yield {
                            type: 'message',
                            content: 'No hotels found for the specified criteria.'
                        };
                    }
                }

                if (parsedResponse.data.flights) {
                    yield {
                        type: 'flights_loading',
                        content: parsedResponse.message
                    };

                    const flightResults = await this.flightsAPI.search(parsedResponse.data);

                    if (flightResults) {
                        yield {
                            type: 'message',
                            content: 'Here are the cheapest flight options for your search.'
                        };

                        yield {
                            type: 'flights',
                            content: flightResults
                        };

                        // Flights Filter to be added in future

                    } else {
                        yield {
                            type: 'message',
                            content: 'No flights found for the specified criteria.'
                        };
                    }
                }
            }



        } catch (error) {
            console.error('Streaming error:', error);
            yield {
                type: 'error',
                content: error.message
            };
        }
    }

    async* handleFilterConversationHotels(request_id, count = 0) {
        const filterResults = await this.hotelsAPI.getAvailableFilters(request_id);
        if (!filterResults) {
            console.log("No filter results found", filterResults);
            count++;
            if (count > 3) {
                console.log("Max retry count reached");
                return;
            }
            // Use async/await with a promise instead of setTimeout
            await new Promise(resolve => setTimeout(resolve, 2000));
            // Recursively call the generator function
            yield* this.handleFilterConversationHotels(request_id, count);
            return;
        }

        if (filterResults) {
            this.hotelsFilterPrompt = this.hotelsFilterPrompt.replace('{filterArray}', JSON.stringify(filterResults));
            let onlyFilterConversations = this.conversationLog.filter(msg => msg.type === 'filter');
            let messages = onlyFilterConversations?.map(msg => ({
                role: msg.sender.toLowerCase(),
                content: msg.message
            })) || [];


            const streamGenerator = bedrockClient.converseStream(messages, this.hotelsFilterPrompt);

            let accumulatedResponse = '';
            let jsonBuffer = '';
            let isCollectingJson = false;
            let openBraces = 0;

            for await (const chunk of streamGenerator) {
                accumulatedResponse += chunk;

                // Count opening and closing braces
                for (let char of chunk) {
                    if (char === '{') openBraces++;
                    if (char === '}') openBraces--;
                }

                // Start collecting JSON when first opening brace is found
                if (chunk.includes('{') && !isCollectingJson) {
                    isCollectingJson = true;
                    jsonBuffer = chunk.slice(chunk.indexOf('{'));
                    // Only yield text before the JSON
                    const textBeforeJson = chunk.slice(0, chunk.indexOf('{'));
                    if (textBeforeJson.trim()) {
                        yield {
                            type: 'thinking',
                            content: textBeforeJson
                        };
                    }
                    continue;
                }

                // Continue collecting JSON
                if (isCollectingJson) {
                    if (!chunk.includes('{')) {
                        jsonBuffer += chunk;
                    }

                    // Check if we have complete JSON (balanced braces)
                    if (openBraces === 0) {
                        isCollectingJson = false;
                        // Don't yield JSON content
                        jsonBuffer = '';
                        continue;
                    }
                    continue;
                }

                // Only yield non-JSON content when we're sure it's not part of JSON
                if (!isCollectingJson && chunk.trim() && !chunk.includes('{') && !chunk.includes('}')) {
                    yield {
                        type: 'thinking',
                        content: chunk
                    };
                }
            }

            if (accumulatedResponse.trim() === "search") {
                // last conversation convert type = "search" 
                this.conversationLog[this.conversationLog.length - 1].type = "search";
                yield* this.handleSearchConversation();
                return;
            }

            this.conversationLog.push({
                sender: 'assistant',
                type: 'filter',
                message: accumulatedResponse,
                time: new Date().toLocaleTimeString(),
                request_id: request_id
            });
            await this.saveConversation(this.session, this.conversationLog);

            const parsedResponse = this.formatResponse(accumulatedResponse);

            if (parsedResponse.parsed) {
                if (parsedResponse.data.type === 'filterCta') {
                    yield {
                        type: 'message',
                        content: '✨ Perfect! Let me show you some smart filters to help find your ideal hotel:'
                    };
                    yield {
                        type: 'filter_cta',
                        content: parsedResponse.data.cta
                    };
                } else if (parsedResponse.data.type === 'applyFilter') {
                    let searchReqBody = this.conversationLog.filter(msg => msg.type === 'searchReqBody')[0].message;
                    const applyFilterResults = await this.hotelsAPI.applyFilters(parsedResponse.data.filters, request_id, searchReqBody);
                    if (applyFilterResults.hotels.length == 0) {
                        yield {
                            type: 'message',
                            content: 'No hotels found for the specified criteria.'
                        };
                        return;
                    }

                    yield {
                        type: 'message',
                        content: '✨ Here are your personalized hotel options based on your preferences! Take a look at these carefully selected choices:'
                    };

                    yield {
                        type: 'hotels',
                        content: applyFilterResults.hotels
                    };
                }
            }
        } else {
            console.log("No filter results found", filterResults);
        }
    }

    isValidJsonString(str) {
        try {
            JSON.parse(str);
            return true;
        } catch (e) {
            return false;
        }
    }

    formatResponse(response) {
        let resp = response.replace(/Vernost:|AVA:|Assistant:|System:|/g, '').trim();

        const firstBrace = resp.indexOf('{');
        const lastBrace = resp.lastIndexOf('}');

        if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
            const jsonString = resp.substring(firstBrace, lastBrace + 1);
            try {
                let parseJson = JSON.parse(jsonString);
                return {
                    parsed: true,
                    data: parseJson,
                    message: this.formatMessage(parseJson)
                };
            } catch (error) {
                return { parsed: false, data: null, message: resp };
            }
        } else {
            return { parsed: false, data: null, message: resp };
        }
    }

    formatMessage(jsonData) {
        try {
            let message = 'Sorry I could not process the request. Please try again';
            if (jsonData.hotels) {
                message = `Thank you for providing the information. Hotel search initiated for ${jsonData.city} from ${jsonData.checkin} to ${jsonData.checkout} for ${jsonData.guests.adults || 1} adults and ${jsonData.guests.childs || 0} children. I will now search for the best hotel options. Please hold on for a moment while I gather the details`;
            } else if (jsonData.flights) {
                message = `Thank you for providing the information. Flight search initiated from ${jsonData.from_airportcode} to ${jsonData.to_airportcode} on ${jsonData.travelDate} for ${jsonData.guests.adults || 1} adults, ${jsonData.guests.childs || 0} children, and ${jsonData.guests.infants || 0} infants. I will now search for the best flight options. Please hold on for a moment while I gather the details`;
            }
            return message;
        } catch (error) {
            return `Error: ${error.message}`;
        }
    }

    getConversationHistory(session) {
        const conversationFile = `./chatHistory/${session}/search.json`;

        const dir = `./chatHistory/${session}`;
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        try {
            if (fs.existsSync(conversationFile)) {
                return JSON.parse(fs.readFileSync(conversationFile, "utf-8"));
            }
            return [];
        } catch (error) {
            console.error("Error reading conversation history:", error);
            return [];
        }
    }

    async saveConversation(session, conversationLog) {
        const conversationFile = `./chatHistory/${session}/search.json`;
        try {
            fs.writeFileSync(conversationFile, JSON.stringify(conversationLog, null, 2));
        } catch (error) {
            console.error("Error saving conversation:", error);
        }
    }
}

module.exports = StreamChatbot;
