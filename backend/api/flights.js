const axios = require("axios");
const config = require('../config.json');

module.exports = class Hotels {
    async callApi(api, body, headers) {
        try {
            let flightsAPI = config.flightsAPI;
            let requestOptions = {
                method: flightsAPI.apis[api].method,
                headers: headers ? { ...flightsAPI.headers, ...headers } : flightsAPI.headers,
                url: flightsAPI.base_url + flightsAPI.apis[api].path,
                timeout: flightsAPI.apis[api].timeout ? flightsAPI.apis[api].timeout : 30000
            }

            if (requestOptions.method == "get") {
                requestOptions.params = body;
            } else {
                requestOptions.data = body;
            }

            let response = await axios(requestOptions);
            return response.data;
        } catch (error) {
            console.error('API call error:', error);
        }
    }
    async search(reqBody) {
        try {

            let response = await this.callApi('search',
                {
                    "flightsearch": [
                        {
                            "origin": reqBody.from_airportcode,
                            "destination": reqBody.to_airportcode,
                            "onwarddate": reqBody.travelDate
                        }
                    ],
                    "hky": "",
                    "id": "",
                    "origin": reqBody.from_airportcode,
                    "destination": reqBody.to_airportcode,
                    "tripType": "ONEWAY",
                    "cabinClass": "E",
                    "adults": reqBody?.guests?.adults || 1,
                    "childs": reqBody?.guests?.childs || 0,
                    "infants": reqBody?.guests?.infants || 0,
                    "isNonStopSearch": false,
                    "onwardDate": reqBody.travelDate,
                    "returnDate": "",
                    "currency": "INR",
                    "flightType": "I",
                    "requestSource": "API",
                    "platform": "WEB",
                    "allAirport": false,
                    "fareIndicator": [],
                    "orderId": null,
                    "onwardPaxId": null,
                    "returnPaxId": null,
                    "widgetName": [
                        ""
                    ]
                });
            if (response.status) {
                let flightList = response.data?.[0].onward_resp;
                flightList = flightList.slice(0, 5);
                flightList.forEach(flight => {
                    delete flight.supplier_bucket;
                });
                return flightList;
            }
            return false;

        } catch (error) {
            console.error('Search Failed:', error);
            return false;
        }
    }
};