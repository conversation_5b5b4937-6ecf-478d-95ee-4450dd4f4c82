const axios = require("axios");
const config = require('../config.json');

module.exports = class Hotels {
    async callApi(api, body, headers) {
        try {
            let hotelAPI = config.hotelsAPI;
            let requestOptions = {
                method: hotelAPI.apis[api].method,
                headers: headers ? { ...hotelAPI.headers, ...headers } : hotelAPI.headers,
                url: hotelAPI.base_url + hotelAPI.apis[api].path,
                timeout: hotelAPI.apis[api].timeout ? hotelAPI.apis[api].timeout : 30000
            }

            if (requestOptions.method == "get") {
                requestOptions.params = body;
            } else {
                requestOptions.data = body;
            }

            let response = await axios(requestOptions);
            return response.data;
        } catch (error) {
            console.error('API call error:', error);
        }
    }
    async search(reqBody) {
        try {
            let response = await this.callApi('autoSuggest', { "keyword": reqBody.city });
            if (response.status) {
                let destination_id = response.data[0].destination_id;
                let search_type = response.data[0].search_type;
                let searchReqBody = {
                    "checkin": reqBody.checkin,
                    "checkout": reqBody.checkout,
                    "type": "accrual",
                    "city": reqBody.city,
                    "destination_id": destination_id,
                    "search_type": search_type,
                    "rooms": [{ adults: reqBody.guests.adults, childs: [] }]
                };
                response = await this.callApi('search', searchReqBody);
                if (response.status) {
                    return this.formatHotelsResponse(response, searchReqBody);
                }
            }
            return false;

        } catch (error) {
            console.error('Search Failed:', error);
            return false;
        }
    }

    formatHotelsResponse(response, searchReqBody) {
        let hotels = response.data.response.slice(0, 5);
        return {
            "request_id": response.data.request_id,
            "searchReqBody": searchReqBody,
            "hotels": hotels.map(hotel => {
                return {
                    hotel_name: hotel.hotel_name,
                    type: hotel.property_type?.[0]?.name || "Hotel",
                    city: hotel.city,
                    headline: hotel.headline,
                    no_of_nights: Math.abs(new Date(searchReqBody.checkout).getTime() - new Date(searchReqBody.checkin).getTime()) / (1000 * 3600 * 24),
                    address: hotel.address,
                    rating: hotel.review_rating.star_rating,
                    price: hotel.price[0].total,
                    currency_code: hotel.price[0].currency_code,
                    location: hotel.location,
                    image: hotel.images.length ? hotel.images[0] : '',
                    hdp_redirect_url: this.createHdpRedirectUrl(hotel, searchReqBody),
                    srp_redirect_url: this.createSrpRedirectUrl(hotel, searchReqBody),
                    filter_action: `clear previous filters if any applied and show hotels with star rating = ${hotel.review_rating.star_rating}, property_type = ${hotel.property_type?.[0]?.name || "Hotel"}, price range min = ${hotel.price[0].total * 0.9}, max = ${hotel.price[0].total * 1.1}`
                }
            })
        };
    }
    createHdpRedirectUrl(hotel, searchReqBody) {
        let redirectUrl = config.hotelsAPI.frontendUrl + 'hoteldetails?';
        redirectUrl += `searchtext=${hotel.hotel_name}&`;
        redirectUrl += `checkin=${searchReqBody.checkin}&checkout=${searchReqBody.checkout}&mpo=accrual&`;
        redirectUrl += `destination_id=${searchReqBody.destination_id}&search_type=${searchReqBody.search_type}&`;
        redirectUrl += `rooms=${searchReqBody.rooms[0].adults}-${searchReqBody.rooms[0].childs?.length}&`;
        redirectUrl += `vid=${hotel.destination_id}`;
        return redirectUrl;
    }
    createSrpRedirectUrl(hotel, searchReqBody) {
        let redirectUrl = config.hotelsAPI.frontendUrl + 'searchdetail?';
        redirectUrl += `region=${searchReqBody.city}&mop=accrual&`;
        redirectUrl += `checkin=${searchReqBody.checkin}&checkout=${searchReqBody.checkout}&mpo=accrual&`;
        redirectUrl += `destination_id=${searchReqBody.destination_id}&search_type=${searchReqBody.search_type}&`;
        redirectUrl += `rooms=${searchReqBody.rooms[0].adults}-${searchReqBody.rooms[0].childs?.length}&`;
        redirectUrl += `vid=${hotel.destination_id}`;
        return redirectUrl;
    }

    async getAvailableFilters(request_id) {
        try {
            let response = await this.callApi('availableFilters', { "request_id": request_id });
            if (response.status) {
                const consideredFilters = ["quick_filter", "price_range", "customer_rating", "star_rating", "stay_options", "property_type", "property_amenities", "hotel_chain", "property_theme", "neighbourhood"];
                const amenityValues = [1, 24, 73, 122, 49, 35]; // specific amenity values to include

                const transformedFilters = {};

                response.data.filters.forEach(filter => {
                    if (consideredFilters.includes(filter.key)) {
                        // Special handling for property_amenities
                        if (filter.key === "property_amenities") {
                            const filteredValues = filter.values.filter(value =>
                                amenityValues.includes(value.value)
                            );

                            if (filteredValues.length > 0) {
                                transformedFilters[filter.key] = {
                                    title: filter.title,
                                    values: filteredValues.map(value => ({
                                        label: value.label,
                                        value: value.value
                                    }))
                                };
                            }
                        } else {
                            // Normal handling for other filters
                            transformedFilters[filter.key] = {
                                title: filter.title,
                                values: filter.values.slice(0, 30).map(value => ({
                                    label: value.label,
                                    value: value.value
                                }))
                            };
                        }
                    }
                });

                return transformedFilters;
            }
            return false;

        } catch (error) {
            console.error('Search Failed:', error);
            return false;
        }
    }

    async applyFilters(filters, request_id, searchReqBody) {
        try {
            let response = await this.callApi('applyFilters', { "request_id": request_id, "filters": filters });
            if (response.status) {
                return this.formatHotelsResponse(response, searchReqBody);
            }
            return false;

        } catch (error) {
            console.error('Search Failed:', error);
            return false;
        }
    }

};
