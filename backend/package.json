{"name": "my-dream-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "node server.js", "build": "ng build", "test": "jest", "lint": "ng lint", "e2e": "ng e2e", "prod:start": "pm2-runtime server.js", "buildProd": "rm -rf dist && webpack --mode production --config webpack.server.config.js && webpack --mode production --config webpack.prod.config.js"}, "private": true, "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.726.1", "@langchain/community": "^0.0.10", "@langchain/openai": "^0.0.10", "@vernost_dev/vms-logger": "1.0.5", "axios": "^0.24.0", "cors": "^2.8.5", "dateformat": "^3.0.3", "dotenv": "^16.4.5", "ejs": "^3.1.8", "express": "^4.17.1", "fs": "0.0.1-security", "http-proxy": "^1.18.1", "langchain": "^0.0.180", "mkdirp": "0.5.5", "moment": "^2.24.0", "mysql": "^2.18.1", "openai": "^4.73.1", "razorpay": "^2.9.0", "readline-sync": "^1.4.10", "redis": "^4.6.8", "uuid": "^8.3.2", "winston": "^3.3.3", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"copy-webpack-plugin": "^6.4.1", "jest": "^29.3.1", "webpack": "^5.75.0", "webpack-cli": "^5.1.1"}}