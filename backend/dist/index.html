<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
    rel="stylesheet">
  <title>VernostGPT</title>
  <link rel="stylesheet" href="css/geniuslanding.css">
</head>

<body>

  <aside class="sidebar">
    <!-- <img class="sidebar__background" src="img/banner.jpg" alt="Element background" /> -->
    <video class="sidebar__background" autoplay muted loop playsinline>
      <source src="banner.mp4" type="video/mp4">
    </video>
    <div class="siderbar_section">
      <h1 class="sidebar__title">Welcome to VernostGPT</h1>
      <p class="sidebar__subtitle">Vernost Intelligence</p>
    </div>
  </aside>
  <div class="heading_top">
    <h2 style="text-align:center;">How can VernostGPT make your day easier?</h2>
    <p>Trips. Rewards. Payments. Conversations that understand you.</p>
  </div>
  <div class="container">
    <div class="card" onclick="toggleCard(this)">
      <img src="img/thirdimg.png" alt="Element" />
      <h3>Craft My Dream Trip</h3>
      <p>Trips curated like playlists. One seamless plan — flights, stays, transfers, done.</p>
      <a href="https://axistraveledgesit.vetravel.io/travel/holidays" target='_blank'><img src="img/button.png" alt="Element" /></a>
    </div>
    <div class="card" onclick="toggleCard(this)">
      <img src="img/secondimg.png" alt="Element" />
      <h3>Stay Somewhere Memorable</h3>
      <p>Hotels that match your vibe, your budget, your style.</p>
      <p>Book in seconds, stay without stress.</p>
      <a href="https://icuracaosit.vetravel.io/hotels" target='_blank'><img src="img/button.png" alt="Element" /></a>
    </div>
    <div class="card" onclick="toggleCard(this)">
      <img src="img/firstimg.png" alt="Element" />
      <h3>Find My Perfect Flight</h3>
      <p>Compare fares. Pick better timings. Spot hidden deals.</p>
      <P>VernostGPT makes it easy to fly smarter.</P>
      <a href="https://icuracaosit.vetravel.io/hotels" target='_blank'><img src="img/button.png"
          alt="Element" /></a>
    </div>
    <div class="card" onclick="toggleCard(this)">
      <img src="img/fourth.png" alt="Element" />
      <h3>Ask VernostGPT</h3>
      <p>We’re training VernostGPT to listen, learn, and respond — 24x7. </p>
      <b>Soon to Be Smarter</b>
    </div>
  </div>

  <script>
    function toggleCard(clickedCard) {
      const allCards = document.querySelectorAll('.card');
      allCards.forEach(card => {
        if (card === clickedCard) {
          card.classList.toggle('active');
        } else {
          card.classList.remove('active');
        }
      });
    }
  </script>
</body>

</html>