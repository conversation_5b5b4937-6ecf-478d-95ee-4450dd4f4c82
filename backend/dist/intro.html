<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <link
    href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
    rel="stylesheet">
  <title>VernostGPT</title>
  <link rel="stylesheet" href="css/style.css">
  </style>
</head>

<body>

  <div class="wrapper">
    <div id="message" class="text-slide" class="toggle-btn" onclick="toggleText()">
      In a world overloaded with apps, loyalty points, travel sites, and payment options, users are overwhelmed, not
      empowered.
    </div>
    <!-- <button class="toggle-btn" onclick="toggleText()">Next</button> -->
  </div>

  <script>
    const message = document.getElementById('message');
    const texts = [
      "In a world overloaded with apps, loyalty points, travel sites, and payment options, users are overwhelmed, not empowered.",
      "Users not chasing features.<br>They’re seeking frictionless flow.<br><br>Users don’t want more apps.<br>They want instant answers.<br><br>Users not loyal to platforms.<br>They’re loyal to experiences that just work.",
      "<h1>VernostGPT</h1><h2>by Vernost Intelligence</h2><br><p>The intelligent engagement layer for today’s users.<br>Personalized. Predictive. Seamless.</p>",
      "<div class='genius'><div class='genius__card'><header class='genius__title-section'><h1>VernostGPT — Suite of Products</h1><p>Each product within VernostGPT is modular.</p></header><br><section class='product__names'><article><small><h3><span>VernostGPT</span><span>Trip</span></h3></small><small><p>Travel Discovery & Booking</p><b>Curated flights, hotels, and packages</b></small></article><article><small><h3><span>VernostGPT</span><span>Miles</span></h3></small><small><p>Rewards & Redemption Engine</p><b>Convert, redeem, and engage with loyalty points</b></small></article><article><small><h3><span>VernostGPT</span><span>Pay</span></h3></small><small><p>Payment Experience Layer</p><b>Smart payments, cashback, and point-based transactions</b></small></article><article><small><h3><span>VernostGPT</span><span>Assist</span></h3></small><small><p>AI-Powered Customer Support</p><b>24x7 support with instant, human-like responses</b></small></article><article><small><h3><span>VernostGPT</span><span>IQ</span></h3></small><small><p>Insights & Personalization</p><b>Behavioral analytics + real-time targeting</b></small></article></section></div></div>",
      "<h1>VernostGPT</h1><h2>Powered by Vernost Intelligence</h2><br><p><b>Voice</b><br>Confident, intuitive, emotionally intelligent</p><br><p><b>Positioning Line<br></b> Your intelligent layer for travel, rewards, and everything in between.</p>",
      "<h3>How It Works</h3><br><p>Input<img src='img/arrowfirst.png' />Intelligence <img src='img/arrowfirst.png' /> Action <img src='img/arrowlast.png' /> Delight</p>",
      "<a href='index.html' target='_blank'><h3>The Vision Ahead</h3><br><p>VernostGPT isn’t just an AI product.<br><br>It’s the future of how people engage with value across systems, brands, and experiences.</p><br><b>One layer. Infinite possibilities.</b></a>"
    ];






    let index = 0;

    // function toggleText() {
    //   message.classList.add('fade-out');
    //   setTimeout(() => {
    //     index = (index + 1) % texts.length;
    //     message.innerHTML = texts[index];
    //     message.classList.remove('fade-out');
    //   }, 300);
    // }

    function toggleText() {
      message.classList.add('fade-out');
      setTimeout(() => {
        index = (index + 1) % texts.length;
        message.innerHTML = texts[index];

        // Remove all message-specific classes
        message.classList.remove('slide_1', 'slide_2', 'slide_3', 'slide_4', 'slide_5', 'slide_6', 'slide_7');

        // Add the class for the current message
        message.classList.add(`slide_${index + 1}`);

        message.classList.remove('fade-out');
      }, 300);
    }
  </script>

</body>

</html>