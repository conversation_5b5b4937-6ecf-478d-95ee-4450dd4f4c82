* {
    box-sizing: border-box;
  }
  body {
    margin: 0;
    font-family: 'Inter', sans-serif;
    background: linear-gradient(227deg, #DDCDF9 -7.67%, #F3EDFF 65.55%);
    padding: 0px;
    margin: 0px;
    background: radial-gradient(circle at 87% 20%, #b3aaff 0%, #f0e6ff 32%, #b3aaff 126%);
  }
  .sidebar {
      width: 100%;
      max-width: 100%;
      color: white;
      padding: 0px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
    }
.siderbar_section{
    width: 100%;
    position: absolute;
    top: 58px;
    left: auto;
}
    .sidebar__background {
        width: 100%;
        height: 160px;
        margin-bottom: 20px;
        object-fit: cover;
    }

    .sidebar__title {
        text-align: center;
        font-family: 'Inter', sans-serif;
        font-size: 32px;
        font-style: normal;
        font-weight: 900;
        line-height: 96%; /* 30.72px */
        letter-spacing: -1.6px;
        background: linear-gradient(88deg, #E0D7FF 48.06%, #1969FF 56.59%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        padding: 0px 0px;
        margin: 0px 0px;
    }
    .sidebar__subtitle {
        color: rgba(255, 255, 255, 0.49);
        text-align: center;
        font-family: 'Inter', sans-serif;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -0.8px;
        padding: 10px 0px;
        margin: 0px 0px;
    }
  .container {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    transition: all 0.3s ease;
    width: 930px;
    margin: 0px auto 0px auto;
    padding: 40px 0px;
  }

  .card {
    flex: 1 1 calc(25% - 1rem);
    background: white;
    padding: 90px 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.5s ease;
    text-align: center;
    border-radius: 32px;
border: 7px solid rgba(0, 0, 0, 0.00);
background: rgba(255, 255, 255, 0.26);
backdrop-filter: blur(100px);
position: relative;
  }
  .card >img{
   width: 64px;
   height: 64px;
  }
  .card >h3{
    color: #6E6984;
    text-align: center;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%; /* 16.8px */
    letter-spacing: -0.56px;
    margin: 10px 0px 0px 0px;
    padding: 0px 0px;
   }
   .card >p{
    display: none;
   }
   .card.active {
    flex: 2 1 calc(42% - 1rem);
    background: #fff;
    color: white;
    transform: scale(1.03);
    padding: 30px 20px;
}
  .card.active >h3{
    color: #6E6984;
    text-align: center;
    font-family: 'Inter', sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%; /* 28.8px */
    letter-spacing: -0.96px;
    margin: 10px 0px 0px 0px;
    padding: 0px 0px;
   }
   .card.active >p{
    color: #666;
    text-align: center;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 120%; /* 16.8px */
    letter-spacing: -0.28px;
    margin: 10px 0px 0px 0px;
    padding: 0px 0px;
    display: block;
   }
   .card.active >p + p{
    color: #666;
    text-align: center;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 120%; /* 16.8px */
    letter-spacing: -0.28px;
    margin: 20px 0px 0px 0px;
    padding: 0px 0px;
    display: block;
   }
   .card >a{
    display: none;
   }
   .card >b{
    display: none;
   }
   .card.active >a{
    text-decoration: none;
    color: inherit;
    margin: 16px 0px 0px 0px;
    display: block;
    position: absolute;
    bottom: 16px;
    left: 0px;
    width: 100%;
   }
   .card.active >b{
    color: #130261;
    text-align: center;
    font-family: 'Inter', sans-serif;
    font-size: 13.309px;
    font-style: normal;
    font-weight: 600;
    line-height: 100%; /* 13.309px */
    letter-spacing: 2.262px;
    text-transform: uppercase;
    display: block;
    position: absolute;
    bottom: 16px;
    left: 0px;
    width: 100%;
   }
   .card.active >a img{
    text-decoration: none;
    color: inherit;
    width: 128px;
    height: 48px;
   }
  .card:not(.active) {
    flex: 1 1 calc(15% - 1rem);
    opacity: 0.7;
  }
.heading_top{
    width: 39%;
    text-align: center;
    margin: 40px auto 0px auto;
}
.heading_top >h2{
    color: #130261;
    text-align: center;
    font-family: 'Inter', sans-serif;
    font-size: 54px;
    font-style: normal;
    font-weight: 600;
    line-height: 100%; /* 54px */
    letter-spacing: -3.78px;
    margin: 0px 0px 0px 0px;
    padding: 0px 0px;
}
.heading_top >p{
    color: #666;
    text-align: center;
    font-family: 'Inter', sans-serif;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: -0.96px;
    margin: 0px 0px 0px 0px;
    padding: 16px 0px;
}
  /* Responsive */
  @media (min-width: 768px) and (max-width: 1023px){
    body{
        height: 100vh;
    }
    .siderbar_section {
        width: 100%;
        position: absolute;
        top: 30px;
        left: auto;
    }
    .card {
      flex: 1 1 30%;
    }
    .card.active {
      flex: 1 1 30%;
    }
    .card:not(.active) {
      /* flex: 1 1 30%; */
    }
    .heading_top {
        width: 70%;
        text-align: center;
        margin: 40px auto 0px auto;
    }
    .container {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        transition: all 0.3s ease;
        width: 100%;
        margin: 0px auto 0px auto;
        padding: 40px 0px;
    }
    .card.active {
        flex: 2 1 calc(30% - 1rem);
        background: #fff;
        color: white;
        transform: scale(1.03);
        padding: 100px 10px;
    }
  }

  @media (min-width: 1024px) and (max-width: 1199px){


    body{
        min-height: 100vh;
    }
    .heading_top >h2 {
        color: #130261;
        text-align: center;
        font-family: 'Inter', sans-serif;
        font-size: 30px;
        font-style: normal;
        font-weight: 600;
        line-height: 100%;
        letter-spacing: normal;
        margin: 0px 0px 0px 0px;
        padding: 0px 0px;
    }
    .heading_top >p {
        color: #666;
        text-align: center;
        font-family: 'Inter', sans-serif;
        font-size: 22px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        letter-spacing: -0.96px;
        margin: 0px 0px 0px 0px;
        padding: 16px 0px;
    }
  }
  @media (min-width: 1366px) and (max-width: 1399px){

    body{
        min-height: 100vh;
    }
  }