* {
    box-sizing: border-box;
  }
  body {
    margin: 0;
    font-family: 'Inter', sans-serif;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    padding: 1rem;
  }

  .wrapper {
    max-width: 950px;
    text-align: center;
    padding: 2rem;
    transition: all 0.5s ease;
  }

  .text-slide {
      color: #130261;
      text-align: center;
      font-family: 'Inter', sans-serif;
      font-size: 48px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%; /* 62.4px */
      letter-spacing: -2.4px;
    opacity: 1;
    transition: opacity 0.5s ease;
    cursor: pointer;
  }
  .text-slide.slide_2 {
      color: rgba(19, 2, 97, 0.70);
  text-align: center;
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%; /* 41.6px */
  letter-spacing: -0.96px;
    opacity: 1;
    transition: opacity 0.5s ease;
  }
  .text-slide >p {
      color: rgba(19, 2, 97, 0.70);
  text-align: center;
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%; /* 41.6px */
  letter-spacing: -0.96px;
  padding: 0px 0px;
  margin: 0px 0px;
  }
  .text-slide >p >b{
      color: rgba(19, 2, 97, 0.70);
  text-align: center;
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-style: normal;
  font-weight: 700;
  line-height: 130%; /* 41.6px */
  letter-spacing: -0.96px;
  padding: 0px 0px;
  margin: 0px 0px;
  }
  .text-slide.slide_3 >h1{
      text-align: center;
      font-family: 'Inter', sans-serif;
      font-size: 151.742px;
      font-style: normal;
      font-weight: 900;
      line-height: 96%; /* 145.672px */
      letter-spacing: -7.587px;
      margin: 0px 0px;
      background: linear-gradient(88deg, #1969FF 48.06%, #002C7E 82.59%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
  }
  .text-slide.slide_3 >h2{
      color: #130261;
      text-align: center;
      font-family: 'Inter', sans-serif;
      font-size: 48px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%; /* 62.4px */
      letter-spacing: -2.4px;
      margin: 0px 0px;
  }
  .text-slide.slide_5 >h1{
      text-align: center;
      font-family: 'Inter', sans-serif;
      font-size: 151.742px;
      font-style: normal;
      font-weight: 900;
      line-height: 96%; /* 145.672px */
      letter-spacing: -7.587px;
      margin: 0px 0px;
      background: linear-gradient(88deg, #1969FF 48.06%, #002C7E 82.59%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
  }
  .text-slide.slide_5 >h2{
      color: #130261;
      text-align: center;
      font-family: 'Inter', sans-serif;
      font-size: 48px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%; /* 62.4px */
      letter-spacing: -2.4px;
      margin: 0px 0px;
  }

  .fade-out {
    opacity: 0;
  }

  .toggle-btn {
    margin-top: 2rem;
    background: #1d1a61;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
  }
  .text-slide.slide_6 >h3, .text-slide.slide_7 >a >h3 {
      color: #130261;
      text-align: center;
      font-family: 'Inter', sans-serif;
      font-size: 48px;
      font-style: normal;
      font-weight: 500;
      line-height: 130%; /* 62.4px */
      letter-spacing: -2.4px;
    opacity: 1;
    transition: opacity 0.5s ease;

  }

  .text-slide.slide_6 >p {
      text-align: center;
      font-family: 'Inter', sans-serif;
      font-size: 54px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: -3.123px;
      background: linear-gradient(88deg, #1969FF 48.06%, #002C7E 82.59%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      display: flex;
          align-items: center;
          gap: 12px;
  }
  .text-slide.slide_6 >p >img{
    width: 40px;
    height: 40px;
  }
  .text-slide.slide_7 >a >p {
      color: rgba(19, 2, 97, 0.70);
      text-align: center;
      font-family: 'Inter', sans-serif;
      font-size: 32px;
      font-style: normal;
      font-weight: 400;
      line-height: 130%; /* 41.6px */
      letter-spacing: -0.96px;
  }
  .text-slide.slide_7 >a >b{
      color: #000;
      text-align: center;
      font-family: 'Inter', sans-serif;
      font-size: 32px;
      font-style: normal;
      font-weight: 700;
      line-height: 130%; /* 41.6px */
      letter-spacing: -0.96px;
  }
  .text-slide.slide_7 >a  {
      color: inherit;
     text-decoration: none;
  }
  /* fourthpage css */
  /* Reset */
* {
margin: 0;
padding: 0;
box-sizing: border-box;
}

body {
font-family: 'Inter', sans-serif;
background-color: #ffffff;
}

/* Block: genius */
.genius {
background-color: #ffffff;
display: flex;
flex-direction: row;
justify-content: center;
width: 100%;
}

.genius__card {
background-color: #ffffff;
overflow: hidden;
border: 5px solid transparent;
position: relative;
}



.product__name_bold {
font-weight: 700;
letter-spacing: -0.93px;
}

.product__name_light {
font-weight: 300;
letter-spacing: -0.93px;
}

.product__descriptions {
position: absolute;
width: 504px;
height: 515px;
top: 217px;
left: 405px;
}

.product__description-container {
position: absolute;
width: 504px;
height: 440px;
top: 75px;
left: 0;
}

.product__title {
position: absolute;
transform: rotate(-90deg);
opacity: 0.7;
font-family: 'Inter', sans-serif;
font-weight: 400;
color: #000000;
font-size: 32px;
letter-spacing: -0.96px;
line-height: 41.6px;
white-space: nowrap;
}

.product__description {
position: absolute;
transform: rotate(-90deg);
opacity: 0.7;
font-family: 'Inter', sans-serif;
font-weight: 400;
color: #000000;
font-size: 20px;
letter-spacing: -0.60px;
line-height: 26px;
white-space: nowrap;
}
.product__descriptions > article >h2{
  color: #130261;
  text-align: center;
  font-family: 'Inter', sans-serif;
  font-size: 48px;
  font-style: normal;
  font-weight: 500;
  line-height: 130%; /* 62.4px */
  letter-spacing: -2.4px;
}
.product__descriptions > article >p{
  color: rgba(19, 2, 97, 0.70);
  text-align: center;
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%; /* 41.6px */
  letter-spacing: -0.96px;

}
.genius__title-section >h1{
  color: #130261;
  text-align: center;
  font-family: 'Inter', sans-serif;
  font-size: 48px;
  font-style: normal;
  font-weight: 500;
  line-height: 130%; /* 62.4px */
  letter-spacing: -2.4px;
  margin: 0px 0px;
}
.genius__title-section >p{
  color: rgba(19, 2, 97, 0.70);
  text-align: center;
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%; /* 41.6px */
  letter-spacing: -0.96px;

}
.product__names >article {
  display: flex;
  flex-direction: row;
  gap: 30px;
  text-align: left;
  align-items: center;
  margin-bottom: 30px;
}
.product__names >article small h3 >span{
  color: #000;
  text-align: right;
  font-family: 'Inter', sans-serif;
  font-size: 55.619px;
  font-style: normal;
  font-weight: 700;
  line-height: 130%; /* 72.305px */
  letter-spacing: -1.669px;
}
.product__names >article small >p{
  color: #000;
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%; /* 41.6px */
  letter-spacing: -0.96px;
}
.product__names >article small >b{
  color: #000;
  font-family: 'Inter', sans-serif;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  line-height: 130%; /* 26px */
  letter-spacing: -0.6px;
}
.product__names >article small{
  line-height: normal;
  display: flex;
  flex-direction: column;
}
.product__names >article small h3 >span + span{
  color: #000;
  font-family: 'Inter', sans-serif;
  font-size: 55.619px;
  font-style: normal;
  font-weight: 300;
  line-height: 130%;
  letter-spacing: -1.669px;
}
.product__names >article small h3 {
  width: 310px;
  text-align: right;
}
@media (min-width: 768px) and (max-width: 1023px){

    .text-slide.slide_6 >p {
        font-size: 2.5rem;
    }
    .text-slide.slide_6 >p >img {
        width: 35px;
        height: 35px;
    }
  }

  @media (max-width: 480px) {
    .text-slide {
      font-size: 1rem;
    }
  }