require('dotenv').config();
const express = require('express');
const cors = require('cors');
const RAGChatbot = require('./api/chatbotBedrocket');
const StreamChatbot = require('./api/streamChatbotV2');
const flights = require('./api/flights');
const hotels = require('./api/hotels');
const config = require('./config.json');
const path = require('path');
const mainMenu = require('./main-menu.json');

const app = express();
app.use(cors());
app.use(express.json());

const hotelsClass = new hotels();
const flightsClass = new flights();


app.use("/", express.static(path.join(__dirname, '/dist/')));

app.post('/chat', async (req, res) => {
    const { message, session } = req.body;

    if (!message) {
        return res.status(400).json({ error: 'No query provided' });
    }

    try {
        const ragChatbot = new RAGChatbot();
        const response = await ragChatbot.getResponse(message, session);
        res.json({ status: true, data: response });
    } catch (error) {
        res.status(500).json({ status: false, error: error.message });
    }
});

app.post('/getChats', async (req, res) => {
    const { session } = req.body;
    console.log(req.body);
    try {
        const ragChatbot = new RAGChatbot();
        const response = await ragChatbot.getChats(session);
        res.json({ status: true, data: response });
    } catch (error) {
        res.status(500).json({ status: false, error: error.message });
    }
});

app.post('/clearChats', async (req, res) => {
    const { session } = req.body;
    console.log(req.body);
    try {
        const ragChatbot = new RAGChatbot();
        const response = await ragChatbot.getChats(session);
        res.json({ status: true, data: response });
    } catch (error) {
        res.status(500).json({ status: false, error: error.message });
    }
});

app.post('/hotelSearch', async (req, res) => {
    const { city, checkin, checkout, guests } = req.body;
    try {
        const response = await hotelsClass.search({ city, checkin, checkout, guests });
        if (!response) {
            return res.json({ status: false, message: 'No hotels found' });
        }
        res.json({ status: true, data: response.hotels });
    } catch (error) {
        res.status(500).json({ status: false, error: error.message });
    }
});

app.post('/flightSearch', async (req, res) => {
    const { from_airportcode, to_airportcode, travelDate, guests } = req.body;
    try {
        const response = await flightsClass.search({ from_airportcode, to_airportcode, travelDate, guests });
        if (!response) {
            return res.json({ status: false, message: 'No flights found' });
        }
        res.json({ status: true, data: response });
    } catch (error) {
        res.status(500).json({ status: false, error: error.message });
    }
});


app.get('/chat/main-menu', async (req, res) => {
    try {
        res.json(mainMenu);
    } catch (error) {
        res.status(500).json({ status: false, error: error.message });
    }
});


app.post('/chat/stream', async (req, res) => {
    console.log(req.body);
    const { message, session } = req.body;
    console.log(message, session);
    if (!message) {
        return res.status(400).json({ error: 'No message provided' });
    }

    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    try {
        const streamChatbot = new StreamChatbot(session, message);
        const messageStream = streamChatbot.getResponse();

        for await (const chunk of messageStream) {
            res.write(`data: ${JSON.stringify(chunk)}\n\n`);
        }

        res.end();
    } catch (error) {
        console.error('Streaming error:', error);
        res.write(`data: ${JSON.stringify({
            type: 'error',
            content: 'An error occurred while processing your request'
        })}\n\n`);
        res.end();
    }
});

app.use('/chat/clearChats', async (req, res) => {
    const { session } = req.body;
    try {
        const ragChatbot = new RAGChatbot();
        const response = await ragChatbot.clearChats(session);
        res.json({ status: true, data: response });
    } catch (error) {
        res.status(500).json({ status: false, error: error.message });
    }
});




app.use("/static/", express.static(path.join(__dirname, '/static/')));

const PORT = config.port || 3000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});

// .env file content:
// OPENAI_API_KEY=your_openai_api_key_here

// package.json dependencies:
// {
//   "dependencies": {
//     "express": "^4.18.2",
//     "cors": "^2.8.5",
//     "dotenv": "^16.3.1",
//     "@langchain/openai": "^0.0.10",
//     "@langchain/community": "^0.0.10",
//     "langchain": "^0.0.180"
//   }
// }
