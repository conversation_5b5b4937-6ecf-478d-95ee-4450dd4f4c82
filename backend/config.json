{"port": 3080, "OPENAI_API_KEY": "OPENAI KEY", "bedrockModel": "meta.llama3-3-70b-instruct-v1:0", "aws": {"region": "us-east-1"}, "bedrock": {"modelId": "meta.llama3-70b-instruct-v1:0"}, "hotelsAPI": {"base_url": "https://icuracaosithotelsapi.vetravel.io/api", "frontendUrl": "https://icuracaosit.vetravel.io/hotels/", "headers": {"Content-Type": "application/json"}, "apis": {"autoSuggest": {"path": "/v1/autosuggest", "method": "post", "timeout": 5000}, "search": {"path": "/v1/hotelsearch", "method": "post", "timeout": 10000}, "availableFilters": {"path": "/v1/getAvailableFilters", "method": "post", "timeout": 5000}, "applyFilters": {"path": "/v1/applyFilters", "method": "post", "timeout": 5000}}}, "flightsAPI": {"base_url": "https://icuracaositflightsapi.vetravel.io/api", "headers": {"Content-Type": "application/json"}, "apis": {"search": {"path": "/v1/searchFlights", "method": "post", "timeout": 30000}}}}