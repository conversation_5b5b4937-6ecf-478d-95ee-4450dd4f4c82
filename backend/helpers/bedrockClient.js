const {
  BedrockRuntimeClient,
  InvokeModelCommand,
  InvokeModelWithResponseStreamCommand,
  ConverseCommand,
  ConverseStreamCommand
} = require('@aws-sdk/client-bedrock-runtime');
const config = require('../config.json');

class BedrockLlamaClient {
  /**
   * Initialize the Bedrock client with AWS credentials.
   */
  constructor() {
    const clientConfig = {
      region: config.aws.region
    };

    if (config.aws.accessKeyId && config.aws.secretAccessKey) {
      clientConfig.credentials = {
        accessKeyId: config.aws.accessKeyId,
        secretAccessKey: config.aws.secretAccessKey
      };
    }

    if (config.bedrock.endpointUrl) {
      clientConfig.endpoint = config.bedrock.endpointUrl;
    }

    this.client = new BedrockRuntimeClient(clientConfig);
    this.modelId = config.bedrock.modelId;
  }

  /**
   * Prepare the payload for Llama models on Bedrock.
   * 
   * @param {string} prompt - The user prompt
   * @param {string} systemPrompt - Optional system prompt to guide the model
   * @param {number} temperature - Controls randomness (0-1)
   * @param {number} maxTokens - Maximum number of tokens to generate
   * @returns {Object} - Formatted payload
   */
  _prepareLlamaPayload(prompt, systemPrompt = null, temperature = 0.7, maxTokens = 2048) {
    const messages = [];

    if (systemPrompt) {
      messages.push({
        role: 'system',
        content: systemPrompt
      });
    }

    messages.push({
      role: 'user',
      content: prompt
    });

    return {
      messages,
      temperature,
      max_gen_len: maxTokens
    };
  }

  /**
   * Generate text using the Llama model.
   * 
   * @param {string} prompt - The user prompt
   * @param {string} systemPrompt - Optional system prompt to guide the model
   * @param {number} temperature - Controls randomness (0-1)
   * @param {number} maxTokens - Maximum number of tokens to generate
   * @returns {Promise<string>} - Generated text
   */
  async generateText(prompt, systemPrompt = null, temperature = 0.7, maxTokens = 2048) {
    const payload = this._prepareLlamaPayload(
      prompt,
      systemPrompt,
      temperature,
      maxTokens
    );

    const command = new InvokeModelCommand({
      modelId: this.modelId,
      body: JSON.stringify(payload)
    });

    try {
      const response = await this.client.send(command);
      const responseBody = JSON.parse(Buffer.from(response.body).toString());
      return responseBody.generation || '';
    } catch (error) {
      console.error('Error generating text:', error, payload);
      throw error;
    }
  }

  /**
   * Generate streaming text using the Llama model.
   * 
   * @param {string} prompt - The user prompt
   * @param {string} systemPrompt - Optional system prompt to guide the model
   * @param {number} temperature - Controls randomness (0-1)
   * @param {number} maxTokens - Maximum number of tokens to generate
   * @returns {AsyncGenerator<string>} - Generated text chunks
   */
  async *generateTextStream(prompt, systemPrompt = null, temperature = 0.7, maxTokens = 2048) {
    const payload = this._prepareLlamaPayload(
      prompt,
      systemPrompt,
      temperature,
      maxTokens
    );

    const command = new InvokeModelWithResponseStreamCommand({
      modelId: this.modelId,
      body: JSON.stringify(payload)
    });

    try {
      const response = await this.client.send(command);
      const stream = response.body;

      for await (const chunk of stream) {
        if (chunk.chunk && chunk.chunk.bytes) {
          const chunkData = JSON.parse(Buffer.from(chunk.chunk.bytes).toString());
          if (chunkData.generation) {
            yield chunkData.generation;
          }

          // Small delay to simulate streaming
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }
    } catch (error) {
      console.error('Error generating text stream:', error, payload);
      throw error;
    }
  }

  /**
   * Prepare the payload for conversation-style interactions
   * 
   * @param {Array} messages - Array of message objects with role and content
   * @param {string} systemPrompt - Optional system prompt
   * @returns {Object} - Formatted payload
   */
  _prepareConversePayload(messages, systemPrompt = null) {
    const conversation = messages?.map(msg => ({
      role: msg.role,
      content: [{
        text: typeof msg.content === 'object' ? JSON.stringify(msg.content) : msg.content
      }]
    }));

    const payload = {
      modelId: this.modelId,
      messages: conversation
    };

    if (systemPrompt) {

      systemPrompt = systemPrompt.replace('{todaysDate}', new Date().toISOString().split('T')[0]);

      payload.system = [{
        text: systemPrompt,
        role: "system"
      }];
    }

    return payload;
  }

  /**
   * Generate response using conversation-style interaction
   * 
   * @param {Array} messages - Array of message objects with role and content
   * @param {string} systemPrompt - Optional system prompt
   * @returns {Promise<string>} - Generated response
   */
  async converse(messages, systemPrompt = null) {
    const payload = this._prepareConversePayload(messages, systemPrompt);
    const command = new ConverseCommand(payload);

    try {
      const response = await this.client.send(command);
      return response?.output?.message?.content[0]?.text || '';
    } catch (error) {
      console.error('Error in conversation:', error, payload);
      throw error;
    }
  }

  /**
   * Generate streaming response using conversation-style interaction
   * 
   * @param {Array} messages - Array of message objects with role and content
   * @param {string} systemPrompt - Optional system prompt
   * @returns {AsyncGenerator<string>} - Generated response chunks
   */
  async *converseStream(messages, systemPrompt = null) {
    const payload = this._prepareConversePayload(messages, systemPrompt);
    const command = new ConverseStreamCommand(payload);

    try {
      const response = await this.client.send(command);
      const stream = response.stream;

      for await (const chunk of stream) {
        if (chunk.contentBlockDelta) {
          if (chunk.contentBlockDelta?.delta?.text) {
            yield chunk.contentBlockDelta?.delta?.text;
          }
        }
      }
    } catch (error) {
      console.error('Error in conversation stream:', error, payload);
      throw error;
    }
  }
}

module.exports = BedrockLlamaClient;
