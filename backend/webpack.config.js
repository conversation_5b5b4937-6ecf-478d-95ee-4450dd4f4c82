var CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = {
    mode: 'production',
    entry: {
        server: './server.js'
    },
    target: 'node',
    output: {
        publicPath: '/',
        path: __dirname + '/build',
        filename: 'server.js',
        chunkFilename: "[id].bundle.js"
    },
    node: {
        __dirname: false,
    },
    plugins: [
        new CopyWebpackPlugin({
            patterns: [
                { from: './config.json', to: './' },
            ],
        }),
    ],
    externals: {
        './config.json': 'require("./config.json")',
        '../config.json': 'require("./config.json")',
        '../../config.json': 'require("./config.json")',
        '../../../config.json': 'require("./config.json")'
    },
    optimization: {
        minimize: false,
    }
}