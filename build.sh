rm -rf build
rm -rf chatbot_build
rm -rf chatbot_build.zip
mkdir chatbot_build
webpack
mv build chatbot_build/static
cd backend
#rm -rf node_modules/
#rm -rf package-lock.json
#npm i --legacy-peer-deps
# unzip -o dist.zip -d node_modules/@vernost_dev/vms-react-components
rm -rf build
webpack
mv build/* ../chatbot_build/
cp -rpf dist ../chatbot_build/
cp -rpf prompts ../chatbot_build/
cd ../chatbot_build
mkdir chatHistory
cd ../
zip -r chatbot_build.zip chatbot_build



echo "------SIT build deployments are started make sure you are connected to the vpn.------"
SFTP_USER="vetravel-demo"
SFTP_HOST="*************"
$SFTP_PASSWORD=""
PRIVATE_KEY=""
LOCAL_FILE="./chatbot_build.zip"
BUILD_PATH="/vetravel-sit/vetravel-demo/chatbot/"
BUILD_DEPLOYMENT_FILE_NAME="deploy.sh"
sftp -i "$PRIVATE_KEY" $SFTP_USER@$SFTP_HOST <<EOF
cd $BUILD_PATH
put $LOCAL_FILE
echo "-----Build zip uploaded successfully-----"
exit 
EOF

ssh -i "$PRIVATE_KEY" $SFTP_USER@$SFTP_HOST <<EOF
cd $BUILD_PATH
echo '$SFTP_PASSWORD' | sudo -S sh $BUILD_DEPLOYMENT_FILE_NAME
exit
EOF

echo "-----SIT build deployments done-----"

echo "Done"