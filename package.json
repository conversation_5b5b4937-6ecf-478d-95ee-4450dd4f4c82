{"name": "ct_travel_fragment", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@vernost_dev/vms-react-components": "^1.8.7", "axios": "^1.6.7", "cors": "^2.8.5", "fs": "^0.0.1-security", "react": "^18.2.0", "react-cookie-consent": "^9.0.0", "react-date-picker": "^11.0.0", "react-dom": "^18.2.0", "react-icons": "^5.3.0", "react-scripts": "5.0.1", "socket.io-client": "^4.8.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "buildWebpack": "webpack --config=webpack.config.js", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.7", "@babel/preset-react": "^7.23.3", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^6.4.1", "css-loader": "^6.8.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.0", "mini-css-extract-plugin": "^2.7.6", "style-loader": "^3.3.3", "webpack-cli": "^5.1.4"}}